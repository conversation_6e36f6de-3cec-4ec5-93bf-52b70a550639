package com.xo.marketplace.database;

import com.xo.backend.database.entity.fees.FeeConfigEntity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.experimental.SuperBuilder;

@Entity
@Data
@EqualsAndHashCode(callSuper = false)
@SuperBuilder
@RequiredArgsConstructor
public class MarketPlaceVenueConfig extends MarketPlaceConfig {

    @Column(name = "venue_id", unique = true)
    private String venueId;

    @OneToOne(mappedBy = "marketPlaceVenueConfig", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private FeeConfigEntity feeConfig;

}

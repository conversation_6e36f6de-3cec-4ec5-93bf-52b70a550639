package com.xo.marketplace.mapper;

import com.xo.backend.database.entity.fees.FeeConfigEntity;
import com.xo.marketplace.dto.VenueFeeConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface VenueFeeConfigMapper {

    @Mapping(target = "id", ignore = true)
    FeeConfigEntity toFeeConfigEntity(VenueFeeConfigDTO venueFeeConfigDTO);

    VenueFeeConfigDTO toVenueFeeConfigDTO(FeeConfigEntity feeConfigEntity);
}

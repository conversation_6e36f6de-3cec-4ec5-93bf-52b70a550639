package com.xo.marketplace.mapper;

import com.xo.marketplace.database.MarketPlaceVenueConfig;
import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface MarketPlaceVenueConfigMapper {

    @Mapping(target = "feeConfig", ignore = true)
    MarketPlaceVenueConfigDTO toMarketPlaceVenueConfigDTO(MarketPlaceVenueConfig marketPlaceVenueConfig);
}

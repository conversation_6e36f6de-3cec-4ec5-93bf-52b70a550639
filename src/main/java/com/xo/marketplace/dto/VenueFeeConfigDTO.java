package com.xo.marketplace.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;

import java.math.BigDecimal;

@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public record VenueFeeConfigDTO(
        String venueId,
        BigDecimal appFeePerc,
        BigDecimal appFeeFixedPerTransaction,
        BigDecimal appFeeFixedPerEntryPass,
        BigDecimal appFeeMin,
        BigDecimal appFeeMax,
        BigDecimal appFeeToCustomerPerc,
        BigDecimal pspFeePerc,
        BigDecimal pspFeeFixed,
        Boolean passPspFees
) {
}

package com.xo.marketplace.controller;

import com.xo.marketplace.service.MarketPlaceVenueConfigService;
import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import jakarta.validation.constraints.Size;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/marketplace/venues/config")
public class MarketPlaceVenueConfigController {

    private final MarketPlaceVenueConfigService marketPlaceVenueConfigService;

    @GetMapping("/{venueId}")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasRole('TENANT_ADMIN')")
    public MarketPlaceVenueConfigDTO getMarketPlaceConfigById(@PathVariable("venueId") String venueId) {
        return marketPlaceVenueConfigService.getMarketPlaceVenueConfigByVenueId(venueId);
    }

    @GetMapping
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasRole('TENANT_ADMIN')")
    public List<MarketPlaceVenueConfigDTO> getMarketPlaceConfigList() {
        return marketPlaceVenueConfigService.getAllMarketPlaceVenueConfigs();
    }

    @PostMapping("/batch")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasRole('TENANT_ADMIN')")
    public ResponseEntity<List<MarketPlaceVenueConfigDTO>> createOrUpdateBatchMarketPlaceVenueConfig(@RequestBody @Size(min = 1, max = 50)
                                                                                                         List<MarketPlaceVenueConfigDTO> marketPlaceVenueConfigDTO) {
        return ResponseEntity.ok(marketPlaceVenueConfigService.createOrUpdateBatchMarketPlaceVenueConfig(marketPlaceVenueConfigDTO));
    }

    @DeleteMapping("/{venueId}")
    @PreAuthorize("hasRole('ADMIN') OR hasRole('PLATFORM_ADMIN') OR hasRole('TENANT_ADMIN')")
    public void deleteMarketPlaceConfig(@PathVariable("venueId") String venueId) {
        marketPlaceVenueConfigService.deleteMarketPlaceVenueConfigById(venueId);
    }

}

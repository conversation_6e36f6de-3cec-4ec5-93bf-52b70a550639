package com.xo.marketplace.service;

import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.fees.FeeConfigEntity;
import com.xo.backend.database.repository.fees.FeeConfigRepository;
import com.xo.backend.error.exceptions.MarketPlaceVenueConfigNotFoundException;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import com.xo.marketplace.database.MarketPlaceVenueConfig;
import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import com.xo.marketplace.dto.VenueFeeConfigDTO;
import com.xo.marketplace.mapper.MarketPlaceVenueConfigMapper;
import com.xo.marketplace.mapper.VenueFeeConfigMapper;
import com.xo.marketplace.repository.MarketPlaceVenueConfigRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MarketPlaceVenueConfigService {

    private final MarketPlaceVenueConfigRepository marketPlaceVenueConfigRepository;
    private final MarketPlaceVenueConfigMapper marketPlaceVenueConfigMapper;
    private final VenueInfoService venueInfoService;
    private final FeeConfigRepository feeConfigRepository;
    private final VenueFeeConfigMapper venueFeeConfigMapper;

    @Transactional
    public MarketPlaceVenueConfigDTO getMarketPlaceVenueConfigByVenueId(String venueId) {

        MarketPlaceVenueConfig venueConfig = marketPlaceVenueConfigRepository.findByVenueId(venueId)
                .orElseThrow(MarketPlaceVenueConfigNotFoundException::new);

        MarketPlaceVenueConfigDTO configDTO = marketPlaceVenueConfigMapper.toMarketPlaceVenueConfigDTO(venueConfig);

        // Get fee configuration if it exists
        VenueFeeConfigDTO feeConfigDTO = feeConfigRepository.findByVenueId(venueId)
                .map(venueFeeConfigMapper::toVenueFeeConfigDTO)
                .orElse(null);

        return MarketPlaceVenueConfigDTO.builder()
                .id(configDTO.id())
                .venueId(configDTO.venueId())
                .visible(configDTO.visible())
                .countryIso(configDTO.countryIso())
                .feeConfig(feeConfigDTO)
                .build();
    }

    @Transactional
    public List<MarketPlaceVenueConfigDTO> getAllMarketPlaceVenueConfigs() {

        List<MarketPlaceVenueConfig> venueConfigs = marketPlaceVenueConfigRepository.findAll();

        // Get all venue IDs to fetch fee configurations
        Set<String> venueIds = venueConfigs.stream()
                .map(MarketPlaceVenueConfig::getVenueId)
                .collect(Collectors.toSet());

        // Get all fee configurations for these venues
        Map<String, VenueFeeConfigDTO> feeConfigMap = feeConfigRepository.findAll().stream()
                .filter(feeConfig -> venueIds.contains(feeConfig.getVenueId()))
                .collect(Collectors.toMap(
                        FeeConfigEntity::getVenueId,
                        venueFeeConfigMapper::toVenueFeeConfigDTO
                ));

        return venueConfigs.stream()
                .map(venueConfig -> {
                    MarketPlaceVenueConfigDTO configDTO = marketPlaceVenueConfigMapper.toMarketPlaceVenueConfigDTO(venueConfig);
                    VenueFeeConfigDTO feeConfigDTO = feeConfigMap.get(venueConfig.getVenueId());

                    return MarketPlaceVenueConfigDTO.builder()
                            .id(configDTO.id())
                            .venueId(configDTO.venueId())
                            .visible(configDTO.visible())
                            .countryIso(configDTO.countryIso())
                            .feeConfig(feeConfigDTO)
                            .build();
                })
                .toList();
    }

    @Transactional
    public List<MarketPlaceVenueConfigDTO> createOrUpdateBatchMarketPlaceVenueConfig(List<MarketPlaceVenueConfigDTO> marketPlaceVenueConfigDTOs) {
        // Extract all venue IDs
        Set<String> allVenueIds = marketPlaceVenueConfigDTOs.stream()
                .map(MarketPlaceVenueConfigDTO::venueId)
                .collect(Collectors.toSet());

        // Find existing venue configs
        List<MarketPlaceVenueConfig> existingConfigs = marketPlaceVenueConfigRepository.findAllByVenueIdIn(allVenueIds);

        // Create a map of existing configs for easy lookup
        Map<String, MarketPlaceVenueConfig> existingConfigMap = existingConfigs.stream()
                .collect(Collectors.toMap(MarketPlaceVenueConfig::getVenueId, config -> config));

        // Find existing fee configs
        Map<String, FeeConfigEntity> existingFeeConfigMap = feeConfigRepository.findAll().stream()
                .filter(feeConfig -> allVenueIds.contains(feeConfig.getVenueId()))
                .collect(Collectors.toMap(FeeConfigEntity::getVenueId, feeConfig -> feeConfig));

        // Identify new venue IDs (those not in existingConfigMap)
        Set<Long> newVenueIds = allVenueIds.stream()
                .filter(id -> !existingConfigMap.containsKey(id))
                .map(Long::valueOf)
                .collect(Collectors.toSet());

        // Results list
        List<MarketPlaceVenueConfig> configsToSave = new ArrayList<>();
        List<FeeConfigEntity> feeConfigsToSave = new ArrayList<>();

        // Process updates for existing configs
        for (MarketPlaceVenueConfigDTO configDTO : marketPlaceVenueConfigDTOs) {
            if (existingConfigMap.containsKey(configDTO.venueId())) {
                // Update existing config
                MarketPlaceVenueConfig existingConfig = existingConfigMap.get(configDTO.venueId());
                //only update visibility flag
                existingConfig.setVisible(configDTO.visible());

                configsToSave.add(existingConfig);

                // Handle fee configuration update/creation
                if (configDTO.feeConfig() != null) {
                    handleFeeConfigUpdate(configDTO.venueId(), configDTO.feeConfig(), existingFeeConfigMap, feeConfigsToSave);
                }
            }
        }

        // Get venue details for new venues
        Map<Long, VenueOverviewDTO> newVenueDetailsMap = Collections.emptyMap();
        if (!newVenueIds.isEmpty()) {
            newVenueDetailsMap = venueInfoService.getVenuesOverview(newVenueIds);
        }

        // Create configs for new venues
        for (MarketPlaceVenueConfigDTO configDTO : marketPlaceVenueConfigDTOs) {
            if (!existingConfigMap.containsKey(configDTO.venueId())) {
                VenueOverviewDTO venueDTO = newVenueDetailsMap.get(Long.valueOf(configDTO.venueId()));
                if (venueDTO != null) {  // Safety check in case venue doesn't exist
                    configsToSave.add(MarketPlaceVenueConfig.builder()
                            .venueId(configDTO.venueId())
                            .visible(configDTO.visible())
                            .countryIso(venueDTO.companyAddress().country().code())
                            .build());

                    // Handle fee configuration for new venues
                    if (configDTO.feeConfig() != null) {
                        handleFeeConfigUpdate(configDTO.venueId(), configDTO.feeConfig(), existingFeeConfigMap, feeConfigsToSave);
                    }
                }
            }
        }

        // Save all configs (both updated and new) at once
        List<MarketPlaceVenueConfig> savedConfigs = marketPlaceVenueConfigRepository.saveAll(configsToSave);

        // Save all fee configs
        if (!feeConfigsToSave.isEmpty()) {
            feeConfigRepository.saveAll(feeConfigsToSave);
        }

        // Build response DTOs with fee configurations
        return buildResponseDTOs(savedConfigs);
    }

    @Transactional
    public void deleteMarketPlaceVenueConfigById(String venueId) {
        marketPlaceVenueConfigRepository.deleteByVenueId(venueId);
        // Also delete associated fee configuration if it exists
        feeConfigRepository.findByVenueId(venueId).ifPresent(feeConfigRepository::delete);
    }

    private void handleFeeConfigUpdate(String venueId, VenueFeeConfigDTO feeConfigDTO,
                                     Map<String, FeeConfigEntity> existingFeeConfigMap,
                                     List<FeeConfigEntity> feeConfigsToSave) {
        if (existingFeeConfigMap.containsKey(venueId)) {
            // Update existing fee config
            FeeConfigEntity existingFeeConfig = existingFeeConfigMap.get(venueId);
            updateFeeConfigEntity(existingFeeConfig, feeConfigDTO);
            feeConfigsToSave.add(existingFeeConfig);
        } else {
            // Create new fee config
            FeeConfigEntity newFeeConfig = venueFeeConfigMapper.toFeeConfigEntity(feeConfigDTO);
            newFeeConfig.setVenueId(venueId); // Ensure venue ID is set correctly
            feeConfigsToSave.add(newFeeConfig);
        }
    }

    private void updateFeeConfigEntity(FeeConfigEntity existingFeeConfig, VenueFeeConfigDTO feeConfigDTO) {
        existingFeeConfig.setAppFeePerc(feeConfigDTO.appFeePerc());
        existingFeeConfig.setAppFeeFixedPerTransaction(feeConfigDTO.appFeeFixedPerTransaction());
        existingFeeConfig.setAppFeeFixedPerEntryPass(feeConfigDTO.appFeeFixedPerEntryPass());
        existingFeeConfig.setAppFeeMin(feeConfigDTO.appFeeMin());
        existingFeeConfig.setAppFeeMax(feeConfigDTO.appFeeMax());
        existingFeeConfig.setAppFeeToCustomerPerc(feeConfigDTO.appFeeToCustomerPerc());
        existingFeeConfig.setPspFeePerc(feeConfigDTO.pspFeePerc());
        existingFeeConfig.setPspFeeFixed(feeConfigDTO.pspFeeFixed());
        existingFeeConfig.setPassPspFees(feeConfigDTO.passPspFees());
    }

    private List<MarketPlaceVenueConfigDTO> buildResponseDTOs(List<MarketPlaceVenueConfig> savedConfigs) {
        // Get all venue IDs to fetch fee configurations
        Set<String> venueIds = savedConfigs.stream()
                .map(MarketPlaceVenueConfig::getVenueId)
                .collect(Collectors.toSet());

        // Get all fee configurations for these venues
        Map<String, VenueFeeConfigDTO> feeConfigMap = feeConfigRepository.findAll().stream()
                .filter(feeConfig -> venueIds.contains(feeConfig.getVenueId()))
                .collect(Collectors.toMap(
                        FeeConfigEntity::getVenueId,
                        venueFeeConfigMapper::toVenueFeeConfigDTO
                ));

        return savedConfigs.stream()
                .map(venueConfig -> {
                    MarketPlaceVenueConfigDTO configDTO = marketPlaceVenueConfigMapper.toMarketPlaceVenueConfigDTO(venueConfig);
                    VenueFeeConfigDTO feeConfigDTO = feeConfigMap.get(venueConfig.getVenueId());

                    return MarketPlaceVenueConfigDTO.builder()
                            .id(configDTO.id())
                            .venueId(configDTO.venueId())
                            .visible(configDTO.visible())
                            .countryIso(configDTO.countryIso())
                            .feeConfig(feeConfigDTO)
                            .build();
                })
                .toList();
    }

}

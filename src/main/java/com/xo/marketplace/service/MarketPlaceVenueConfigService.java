package com.xo.marketplace.service;

import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.fees.FeeConfigEntity;
import com.xo.backend.database.repository.fees.FeeConfigRepository;
import com.xo.backend.error.exceptions.MarketPlaceVenueConfigNotFoundException;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import com.xo.marketplace.database.MarketPlaceVenueConfig;
import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import com.xo.marketplace.dto.VenueFeeConfigDTO;
import com.xo.marketplace.mapper.MarketPlaceVenueConfigMapper;
import com.xo.marketplace.mapper.VenueFeeConfigMapper;
import com.xo.marketplace.repository.MarketPlaceVenueConfigRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MarketPlaceVenueConfigService {

    private final MarketPlaceVenueConfigRepository marketPlaceVenueConfigRepository;
    private final MarketPlaceVenueConfigMapper marketPlaceVenueConfigMapper;
    private final VenueInfoService venueInfoService;
    private final FeeConfigRepository feeConfigRepository;
    private final VenueFeeConfigMapper venueFeeConfigMapper;

    @Transactional
    public MarketPlaceVenueConfigDTO getMarketPlaceVenueConfigByVenueId(String venueId) {
        MarketPlaceVenueConfig venueConfig = marketPlaceVenueConfigRepository.findByVenueId(venueId)
                .orElseThrow(MarketPlaceVenueConfigNotFoundException::new);

        return marketPlaceVenueConfigMapper.toMarketPlaceVenueConfigDTO(venueConfig);
    }

    @Transactional
    public List<MarketPlaceVenueConfigDTO> getAllMarketPlaceVenueConfigs() {
        List<MarketPlaceVenueConfig> venueConfigs = marketPlaceVenueConfigRepository.findAll();
        return buildVenueConfigDTOs(venueConfigs);
    }

    @Transactional
    public List<MarketPlaceVenueConfigDTO> createOrUpdateBatchMarketPlaceVenueConfig(List<MarketPlaceVenueConfigDTO> marketPlaceVenueConfigDTOs) {
        Set<String> allVenueIds = extractVenueIds(marketPlaceVenueConfigDTOs);

        Map<String, MarketPlaceVenueConfig> existingConfigMap = getExistingVenueConfigs(allVenueIds);
        Map<String, FeeConfigEntity> existingFeeConfigMap = getExistingFeeConfigs(allVenueIds);

        Set<Long> newVenueIds = getNewVenueIds(allVenueIds, existingConfigMap);
        Map<Long, VenueOverviewDTO> newVenueDetailsMap = getVenueDetails(newVenueIds);

        List<MarketPlaceVenueConfig> configsToSave = new ArrayList<>();
        List<FeeConfigEntity> feeConfigsToSave = new ArrayList<>();

        processVenueConfigs(marketPlaceVenueConfigDTOs, existingConfigMap, existingFeeConfigMap,
                           newVenueDetailsMap, configsToSave, feeConfigsToSave);

        return saveAndBuildResponse(configsToSave, feeConfigsToSave);
    }



    @Transactional
    public void deleteMarketPlaceVenueConfigById(String venueId) {
        marketPlaceVenueConfigRepository.deleteByVenueId(venueId);
        feeConfigRepository.findByVenueId(venueId).ifPresent(feeConfigRepository::delete);
    }

    // Helper methods for better readability and maintainability

    private Set<String> extractVenueIds(List<MarketPlaceVenueConfigDTO> configDTOs) {
        return configDTOs.stream()
                .map(MarketPlaceVenueConfigDTO::venueId)
                .collect(Collectors.toSet());
    }

    private Map<String, MarketPlaceVenueConfig> getExistingVenueConfigs(Set<String> venueIds) {
        return marketPlaceVenueConfigRepository.findAllByVenueIdIn(venueIds).stream()
                .collect(Collectors.toMap(MarketPlaceVenueConfig::getVenueId, config -> config));
    }

    private Map<String, FeeConfigEntity> getExistingFeeConfigs(Set<String> venueIds) {
        return feeConfigRepository.findAll().stream()
                .filter(feeConfig -> venueIds.contains(feeConfig.getVenueId()))
                .collect(Collectors.toMap(FeeConfigEntity::getVenueId, feeConfig -> feeConfig));
    }

    private Set<Long> getNewVenueIds(Set<String> allVenueIds, Map<String, MarketPlaceVenueConfig> existingConfigMap) {
        return allVenueIds.stream()
                .filter(id -> !existingConfigMap.containsKey(id))
                .map(Long::valueOf)
                .collect(Collectors.toSet());
    }

    private Map<Long, VenueOverviewDTO> getVenueDetails(Set<Long> newVenueIds) {
        return newVenueIds.isEmpty() ? Collections.emptyMap() : venueInfoService.getVenuesOverview(newVenueIds);
    }

    private void processVenueConfigs(List<MarketPlaceVenueConfigDTO> configDTOs,
                                   Map<String, MarketPlaceVenueConfig> existingConfigMap,
                                   Map<String, FeeConfigEntity> existingFeeConfigMap,
                                   Map<Long, VenueOverviewDTO> newVenueDetailsMap,
                                   List<MarketPlaceVenueConfig> configsToSave,
                                   List<FeeConfigEntity> feeConfigsToSave) {

        for (MarketPlaceVenueConfigDTO configDTO : configDTOs) {
            if (existingConfigMap.containsKey(configDTO.venueId())) {
                processExistingVenueConfig(configDTO, existingConfigMap, existingFeeConfigMap, configsToSave, feeConfigsToSave);
            } else {
                processNewVenueConfig(configDTO, newVenueDetailsMap, existingFeeConfigMap, configsToSave, feeConfigsToSave);
            }
        }
    }

    private void processExistingVenueConfig(MarketPlaceVenueConfigDTO configDTO,
                                          Map<String, MarketPlaceVenueConfig> existingConfigMap,
                                          Map<String, FeeConfigEntity> existingFeeConfigMap,
                                          List<MarketPlaceVenueConfig> configsToSave,
                                          List<FeeConfigEntity> feeConfigsToSave) {

        MarketPlaceVenueConfig existingConfig = existingConfigMap.get(configDTO.venueId());
        existingConfig.setVisible(configDTO.visible());
        configsToSave.add(existingConfig);

        if (configDTO.feeConfig() != null) {
            processFeeConfig(configDTO.venueId(), configDTO.feeConfig(), existingFeeConfigMap, feeConfigsToSave);
        }
    }

    private void processNewVenueConfig(MarketPlaceVenueConfigDTO configDTO,
                                     Map<Long, VenueOverviewDTO> newVenueDetailsMap,
                                     Map<String, FeeConfigEntity> existingFeeConfigMap,
                                     List<MarketPlaceVenueConfig> configsToSave,
                                     List<FeeConfigEntity> feeConfigsToSave) {

        VenueOverviewDTO venueDTO = newVenueDetailsMap.get(Long.valueOf(configDTO.venueId()));
        if (venueDTO != null) {
            MarketPlaceVenueConfig newConfig = MarketPlaceVenueConfig.builder()
                    .venueId(configDTO.venueId())
                    .visible(configDTO.visible())
                    .countryIso(venueDTO.companyAddress().country().code())
                    .build();
            configsToSave.add(newConfig);

            if (configDTO.feeConfig() != null) {
                processFeeConfig(configDTO.venueId(), configDTO.feeConfig(), existingFeeConfigMap, feeConfigsToSave);
            }
        }
    }

    private void processFeeConfig(String venueId, VenueFeeConfigDTO feeConfigDTO,
                                Map<String, FeeConfigEntity> existingFeeConfigMap,
                                List<FeeConfigEntity> feeConfigsToSave) {

        if (existingFeeConfigMap.containsKey(venueId)) {
            FeeConfigEntity existingFeeConfig = existingFeeConfigMap.get(venueId);
            updateFeeConfigEntity(existingFeeConfig, feeConfigDTO);
            feeConfigsToSave.add(existingFeeConfig);
        } else {
            FeeConfigEntity newFeeConfig = venueFeeConfigMapper.toFeeConfigEntity(feeConfigDTO);
            newFeeConfig.setVenueId(venueId);
            feeConfigsToSave.add(newFeeConfig);
        }
    }

    private void updateFeeConfigEntity(FeeConfigEntity existingFeeConfig, VenueFeeConfigDTO feeConfigDTO) {
        existingFeeConfig.setAppFeePerc(feeConfigDTO.appFeePerc());
        existingFeeConfig.setAppFeeFixedPerTransaction(feeConfigDTO.appFeeFixedPerTransaction());
        existingFeeConfig.setAppFeeFixedPerEntryPass(feeConfigDTO.appFeeFixedPerEntryPass());
        existingFeeConfig.setAppFeeMin(feeConfigDTO.appFeeMin());
        existingFeeConfig.setAppFeeMax(feeConfigDTO.appFeeMax());
        existingFeeConfig.setAppFeeToCustomerPerc(feeConfigDTO.appFeeToCustomerPerc());
        existingFeeConfig.setPspFeePerc(feeConfigDTO.pspFeePerc());
        existingFeeConfig.setPspFeeFixed(feeConfigDTO.pspFeeFixed());
        existingFeeConfig.setPassPspFees(feeConfigDTO.passPspFees());
    }

    private List<MarketPlaceVenueConfigDTO> saveAndBuildResponse(List<MarketPlaceVenueConfig> configsToSave,
                                                               List<FeeConfigEntity> feeConfigsToSave) {
        List<MarketPlaceVenueConfig> savedConfigs = marketPlaceVenueConfigRepository.saveAll(configsToSave);

        if (!feeConfigsToSave.isEmpty()) {
            feeConfigRepository.saveAll(feeConfigsToSave);
        }

        return buildVenueConfigDTOs(savedConfigs);
    }

    private MarketPlaceVenueConfigDTO buildVenueConfigDTO(MarketPlaceVenueConfig venueConfig) {
        MarketPlaceVenueConfigDTO configDTO = marketPlaceVenueConfigMapper.toMarketPlaceVenueConfigDTO(venueConfig);
        VenueFeeConfigDTO feeConfigDTO = feeConfigRepository.findByVenueId(venueConfig.getVenueId())
                .map(venueFeeConfigMapper::toVenueFeeConfigDTO)
                .orElse(null);

        return MarketPlaceVenueConfigDTO.builder()
                .id(configDTO.id())
                .venueId(configDTO.venueId())
                .visible(configDTO.visible())
                .countryIso(configDTO.countryIso())
                .feeConfig(feeConfigDTO)
                .build();
    }

    private List<MarketPlaceVenueConfigDTO> buildVenueConfigDTOs(List<MarketPlaceVenueConfig> venueConfigs) {
        Set<String> venueIds = venueConfigs.stream()
                .map(MarketPlaceVenueConfig::getVenueId)
                .collect(Collectors.toSet());

        Map<String, VenueFeeConfigDTO> feeConfigMap = feeConfigRepository.findAll().stream()
                .filter(feeConfig -> venueIds.contains(feeConfig.getVenueId()))
                .collect(Collectors.toMap(
                        FeeConfigEntity::getVenueId,
                        venueFeeConfigMapper::toVenueFeeConfigDTO
                ));

        return venueConfigs.stream()
                .map(venueConfig -> {
                    MarketPlaceVenueConfigDTO configDTO = marketPlaceVenueConfigMapper.toMarketPlaceVenueConfigDTO(venueConfig);
                    VenueFeeConfigDTO feeConfigDTO = feeConfigMap.get(venueConfig.getVenueId());

                    return MarketPlaceVenueConfigDTO.builder()
                            .id(configDTO.id())
                            .venueId(configDTO.venueId())
                            .visible(configDTO.visible())
                            .countryIso(configDTO.countryIso())
                            .feeConfig(feeConfigDTO)
                            .build();
                })
                .toList();
    }

}

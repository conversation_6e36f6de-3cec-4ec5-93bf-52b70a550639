package com.xo.marketplace.service;

import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.fees.FeeConfigEntity;
import com.xo.backend.error.exceptions.MarketPlaceVenueConfigNotFoundException;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import com.xo.marketplace.database.MarketPlaceVenueConfig;
import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import com.xo.marketplace.mapper.MarketPlaceVenueConfigMapper;
import com.xo.marketplace.mapper.VenueFeeConfigMapper;
import com.xo.marketplace.repository.MarketPlaceVenueConfigRepository;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class MarketPlaceVenueConfigService {

    private final MarketPlaceVenueConfigRepository marketPlaceVenueConfigRepository;
    private final MarketPlaceVenueConfigMapper marketPlaceVenueConfigMapper;
    private final VenueInfoService venueInfoService;
    private final VenueFeeConfigMapper venueFeeConfigMapper;

    @Transactional
    public MarketPlaceVenueConfigDTO getMarketPlaceVenueConfigByVenueId(String venueId) {
        MarketPlaceVenueConfig venueConfig = marketPlaceVenueConfigRepository.findByVenueId(venueId)
                .orElseThrow(MarketPlaceVenueConfigNotFoundException::new);

        return marketPlaceVenueConfigMapper.toMarketPlaceVenueConfigDTO(venueConfig);
    }

    @Transactional
    public List<MarketPlaceVenueConfigDTO> getAllMarketPlaceVenueConfigs() {
        List<MarketPlaceVenueConfig> venueConfigs = marketPlaceVenueConfigRepository.findAll();
        return venueConfigs.stream()
                .map(marketPlaceVenueConfigMapper::toMarketPlaceVenueConfigDTO)
                .toList();
    }

    @Transactional
    public List<MarketPlaceVenueConfigDTO> createOrUpdateBatchMarketPlaceVenueConfig(List<MarketPlaceVenueConfigDTO> marketPlaceVenueConfigDTOs) {
        Set<String> allVenueIds = extractVenueIds(marketPlaceVenueConfigDTOs);

        Map<String, MarketPlaceVenueConfig> existingConfigMap = getExistingVenueConfigs(allVenueIds);
        Set<Long> newVenueIds = getNewVenueIds(allVenueIds, existingConfigMap);
        Map<Long, VenueOverviewDTO> newVenueDetailsMap = getVenueDetails(newVenueIds);

        List<MarketPlaceVenueConfig> configsToSave = new ArrayList<>();

        for (MarketPlaceVenueConfigDTO configDTO : marketPlaceVenueConfigDTOs) {
            MarketPlaceVenueConfig config = processVenueConfig(configDTO, existingConfigMap, newVenueDetailsMap);
            if (config != null) {
                configsToSave.add(config);
            }
        }

        List<MarketPlaceVenueConfig> savedConfigs = marketPlaceVenueConfigRepository.saveAll(configsToSave);
        return savedConfigs.stream()
                .map(marketPlaceVenueConfigMapper::toMarketPlaceVenueConfigDTO)
                .toList();
    }



    @Transactional
    public void deleteMarketPlaceVenueConfigById(String venueId) {
        // With CASCADE delete, the fee config will be automatically deleted
        marketPlaceVenueConfigRepository.deleteByVenueId(venueId);
    }

    // Helper methods for better readability and maintainability

    private Set<String> extractVenueIds(List<MarketPlaceVenueConfigDTO> configDTOs) {
        return configDTOs.stream()
                .map(MarketPlaceVenueConfigDTO::venueId)
                .collect(Collectors.toSet());
    }

    private Map<String, MarketPlaceVenueConfig> getExistingVenueConfigs(Set<String> venueIds) {
        return marketPlaceVenueConfigRepository.findAllByVenueIdIn(venueIds).stream()
                .collect(Collectors.toMap(MarketPlaceVenueConfig::getVenueId, config -> config));
    }

    private Set<Long> getNewVenueIds(Set<String> allVenueIds, Map<String, MarketPlaceVenueConfig> existingConfigMap) {
        return allVenueIds.stream()
                .filter(id -> !existingConfigMap.containsKey(id))
                .map(Long::valueOf)
                .collect(Collectors.toSet());
    }

    private Map<Long, VenueOverviewDTO> getVenueDetails(Set<Long> newVenueIds) {
        return newVenueIds.isEmpty() ? Collections.emptyMap() : venueInfoService.getVenuesOverview(newVenueIds);
    }

    private MarketPlaceVenueConfig processVenueConfig(MarketPlaceVenueConfigDTO configDTO,
                                                     Map<String, MarketPlaceVenueConfig> existingConfigMap,
                                                     Map<Long, VenueOverviewDTO> newVenueDetailsMap) {

        MarketPlaceVenueConfig venueConfig;

        if (existingConfigMap.containsKey(configDTO.venueId())) {
            // Update existing venue config
            venueConfig = existingConfigMap.get(configDTO.venueId());
            venueConfig.setVisible(configDTO.visible());
        } else {
            // Create new venue config
            VenueOverviewDTO venueDTO = newVenueDetailsMap.get(Long.valueOf(configDTO.venueId()));
            if (venueDTO == null) {
                return null;
            }

            venueConfig = MarketPlaceVenueConfig.builder()
                    .venueId(configDTO.venueId())
                    .visible(configDTO.visible())
                    .countryIso(venueDTO.companyAddress().country().code())
                    .build();
        }

        // Handle fee config (common logic for both create and update)
        setFeeConfigIfProvided(configDTO, venueConfig);

        return venueConfig;
    }

    private void setFeeConfigIfProvided(MarketPlaceVenueConfigDTO configDTO, MarketPlaceVenueConfig venueConfig) {
        if (configDTO.feeConfig() != null) {
            FeeConfigEntity feeConfig = venueFeeConfigMapper.toFeeConfigEntity(configDTO.feeConfig());
            feeConfig.setVenueId(configDTO.venueId());
            venueConfig.setFeeConfig(feeConfig);
        }
    }


}

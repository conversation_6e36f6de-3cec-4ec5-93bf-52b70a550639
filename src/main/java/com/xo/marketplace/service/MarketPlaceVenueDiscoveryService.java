package com.xo.marketplace.service;

import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.entity.events.EventStatus;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.error.exceptions.InvalidCountryCodeException;
import com.xo.backend.mappers.EventDetailsMapper;
import com.xo.backend.model.dto.EventVenueDTO;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import com.xo.marketplace.database.CountryCode;
import com.xo.marketplace.database.MarketPlaceVenueConfig;
import com.xo.marketplace.repository.MarketPlaceVenueConfigRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class MarketPlaceVenueDiscoveryService {

    private final MarketPlaceVenueConfigRepository marketPlaceVenueConfigRepository;
    private final VenueInfoService venueInfoService;
    private final EventRepository eventRepository;
    private final EventDetailsMapper eventMapper;

    @Transactional(readOnly = true)
    public Page<VenueOverviewDTO> getVisibleMarketPlaceVenuePage(CountryCode countryCode, Pageable pageable) {
        Page<Long> venueIdsPage;
        if(countryCode != null && CountryCode.EMPTY!=countryCode) {
            venueIdsPage = marketPlaceVenueConfigRepository.findByVisibleTrueAndCountryIso(countryCode.name(), pageable)
                    .map(MarketPlaceVenueConfig::getVenueId)
                    .map(Long::valueOf);
        } else {
            venueIdsPage = marketPlaceVenueConfigRepository.findByVisibleTrue(pageable)
                    .map(MarketPlaceVenueConfig::getVenueId)
                    .map(Long::valueOf);
        }
        Map<Long, VenueOverviewDTO> venueMap = venueInfoService.getVenuesOverview(venueIdsPage.getContent());
        List<VenueOverviewDTO> venueList = new ArrayList<>(venueMap.values());

        return new PageImpl<>(venueList, venueIdsPage.getPageable(), venueIdsPage.getTotalElements());
    }

    @Transactional(readOnly = true)
    public Page<EventVenueDTO> getVisibleMarketPlaceVenueEvents(String venueId, Pageable pageable) {
        boolean isVenueVisible = marketPlaceVenueConfigRepository.existsByVenueIdAndVisibleTrue(venueId);
        VenueDTO venueDTO=venueInfoService.getVenueDetails(venueId);
        if (!isVenueVisible) {
            log.warn("Venue not found or not visible: {}", venueId);
            return Page.empty();
        }

        return eventRepository
                .findMarketplaceEventsByVenueId(venueId, EventStatus.PUBLISHED, Instant.now(), pageable)
                .map((EventEntity event) -> eventMapper.toEventVenueDTO(event,venueDTO));
    }
}

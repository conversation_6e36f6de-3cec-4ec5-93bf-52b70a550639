package com.xo.backend.utlis;

import com.xo.backend.config.properties.BookingProperties;
import com.xo.backend.database.entity.bookings.*;
import com.xo.backend.database.repository.booking.BookingTierRepository;
import com.xo.backend.error.exceptions.BookingRequestException;
import com.xo.backend.error.exceptions.BookingTierNotFoundException;
import com.xo.backend.model.dto.BookingItemDTO;
import com.xo.backend.model.dto.fees.BookingFeeDTO;
import com.xo.backend.service.AdminBookingTierService;
import com.xo.backend.service.FeeCalculationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xo.backend.database.entity.bookings.BookingTierType.TICKET;

@Component
@Slf4j
@RequiredArgsConstructor
public class BookingHelper {

    private final BookingTierRepository bookingTierRepository;
    private final BookingProperties bookingProperties;
    private final AdminBookingTierService adminBookingTierService;
    private final FeeCalculationService feeCalculationService;

    @Transactional(readOnly = true)
    public BookingType getBookingType(List<BookingItemDTO> bookingItems) {
        boolean hasTicket = false;
        boolean hasReservation = false;
        List<Integer> bookingTierIds = bookingItems.stream().map(BookingItemDTO::bookingTierId).toList();

        List<BookingTierEntity> bookingTiers = bookingTierRepository.findAllById(bookingTierIds);
        for (BookingTierEntity bookingTierEntity : bookingTiers) {

            if (bookingTierEntity.getType().equals(TICKET)) {
                hasTicket = true;
            } else if (bookingTierEntity.getType().equals(BookingTierType.RESERVATION)) {
                hasReservation = true;
            }

            if (hasTicket && hasReservation) {
                return BookingType.MIXED;
            }
        }
        return hasTicket ? BookingType.TICKET : BookingType.RESERVATION;
    }

    public void validateBooking(BookingType bookingType, List<BookingItemDTO> bookingItems) {
        if (!bookingProperties.isMixedBookingAllowed() && bookingType.equals(BookingType.MIXED)) {
            throw new BookingRequestException("Cannot have both tickets and reservations tiers in a single booking");
        }

        //only allow single booking tier of type reservation
        if (bookingType.equals(BookingType.RESERVATION) && bookingItems.size() > 1) {
            throw new BookingRequestException("Cannot have multiple reservation tiers in a single booking");
        }
    }

    public void validateUserBooking(BookingType bookingType, List<BookingItemDTO> bookingItems) {

        validateBooking(bookingType, bookingItems);

        //check booking items do not exceed limit if user not admin
        if (isBookingItemsQuantityAboveLimit(bookingItems, bookingType)) {
            throw new BookingRequestException("booking quantity exceeds limit");
        }
    }

    private boolean isBookingItemsQuantityAboveLimit(List<BookingItemDTO> bookingItems, BookingType bookingType) {
        int maxQuantityAllowed = bookingType.equals(BookingType.TICKET) ?
                bookingProperties.getMaxTicketPerUserPerEvent() :
                bookingProperties.getMaxReservationPerUserPerEvent();
        return bookingItems.stream()
                .anyMatch(bookingItem -> bookingItem.quantity() > maxQuantityAllowed);
    }

    public void calculateBookingPaymentAmount(BookingEntity bookingEntity, List<BookingItemEntity> bookingItemEntityList) {
        BigDecimal netAmount = calculateBookingNetPaymentAmount(bookingItemEntityList);
        bookingEntity.setNetAmount(netAmount);

        if (netAmount.compareTo(BigDecimal.ZERO) > 0) {
            updateBookingFees(bookingEntity);
        } else {
            bookingEntity.setTotalFee(BigDecimal.ZERO);
            bookingEntity.setApplicationFee(BigDecimal.ZERO);
            bookingEntity.setPspFee(BigDecimal.ZERO);
        }

        bookingEntity.setTotalAmount(bookingEntity.getNetAmount().add(bookingEntity.getTotalFee()));
    }

    private BigDecimal calculateBookingNetPaymentAmount(List<BookingItemEntity> bookingItems) {
        return bookingItems.stream()
                .map(BookingItemEntity::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public void updateBookingFees(BookingEntity bookingEntity) {
        BookingFeeDTO bookingFeeDTO = feeCalculationService.calculateBookingFees(bookingEntity);
        bookingEntity.setTotalFee(bookingFeeDTO.totalFee());
        bookingEntity.setApplicationFee(bookingFeeDTO.applicationFee());
        bookingEntity.setPspFee(bookingFeeDTO.pspFee());
    }

    public List<BookingItemEntity> prepareBookingItems(
            List<BookingItemDTO> bookingItemDTOList,
            BookingEntity booking,
            int eventId,
            boolean updateAvailability) {

        // Filter out items with quantity 0
        List<BookingItemDTO> validBookingItems = bookingItemDTOList.stream()
                .filter(item -> item.quantity() > 0)
                .toList();

        // 1. Collect all unique bookingTierIds
        List<Integer> bookingTierIds = validBookingItems.stream()
                .map(BookingItemDTO::bookingTierId)
                .distinct()
                .toList();

        // 2. Fetch all BookingTiers in one DB call
        List<BookingTierEntity> bookingTiers = bookingTierRepository.findAllById(bookingTierIds);

        // 3. Map id -> BookingTierEntity
        Map<Integer, BookingTierEntity> bookingTierMap = bookingTiers.stream()
                .collect(Collectors.toMap(BookingTierEntity::getId, Function.identity()));

        List<BookingItemEntity> bookingItemEntityList = new ArrayList<>();
        boolean isPreSaleDiscountAvailable = isPreSaleDiscountAvailable(booking);
        booking.setHasPreSaleDiscounts(isPreSaleDiscountAvailable);

        for (BookingItemDTO bookingItem : validBookingItems) {
            BookingTierEntity bookingTier = bookingTierMap.get(bookingItem.bookingTierId());
            if (bookingTier == null) {
                throw new BookingTierNotFoundException();
            }

            BigDecimal purchasePrice = isPreSaleDiscountAvailable
                    ? bookingTier.getPreSaleDiscountedPrice()
                    : bookingTier.getPayOnlinePrice();

            // Check availability
            adminBookingTierService.checkBookingTierAvailability(
                    bookingItem.bookingTierId(),
                    bookingItem.quantity(),
                    bookingItem.numOfPeople(),
                    eventId,
                    updateAvailability
            );

            bookingItemEntityList.add(BookingItemEntity.builder()
                    .bookingTierId(bookingItem.bookingTierId())
                    .bookingTier(bookingTier)
                    .bookingId(booking.getId())
                    .numOfPeople(bookingItem.numOfPeople())
                    .quantity(bookingItem.quantity())
                    .purchasePrice(purchasePrice)
                    .totalPrice(purchasePrice.multiply(BigDecimal.valueOf(bookingItem.quantity())))
                    .build());
        }
        return bookingItemEntityList;
    }

    public boolean isPreSaleDiscountAvailable(BookingEntity booking) {
        return booking.getEvent().isPreSaleDiscountAvailable();
    }

    public boolean hasPreSaleDiscountsExpired(BookingEntity booking) {
        return Boolean.TRUE.equals(booking.getHasPreSaleDiscounts())
                && !booking.getEvent().isPreSaleDiscountAvailable();
    }

    public BookingConfirmationRequirementEnum isBookingConfirmationRequired(BookingEntity booking) {
        if (booking.getBookingItems().stream()
                .anyMatch(bookingItem -> bookingItem.getBookingTier().getRequireConfirmation() == BookingConfirmationRequirementEnum.YES)) {
            return BookingConfirmationRequirementEnum.YES;
        }
        return BookingConfirmationRequirementEnum.NO;
    }

    public boolean isBookingConfirmationRequired(BookingConfirmationRequirementEnum requirementEnum) {
        return BookingConfirmationRequirementEnum.YES == requirementEnum;
    }
}

package com.xo.backend.service;

import com.google.common.collect.Range;
import com.google.zxing.WriterException;
import com.xo.backend.client.email.EmailService;
import com.xo.backend.client.email.QrCodeService;
import com.xo.backend.client.user.UserInfoService;
import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.bookings.*;
import com.xo.backend.database.entity.entrypass.EntryPassEntity;
import com.xo.backend.database.entity.events.EventAttributeEntity;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.entity.payments.TransactionStatus;
import com.xo.backend.database.repository.booking.BookingRepository;
import com.xo.backend.database.repository.booking.BookingTierRepository;
import com.xo.backend.database.repository.entry_pass.EntryPassRepository;
import com.xo.backend.database.repository.event.EventRepository;
import com.xo.backend.database.repository.payments.PaymentTransactionRepository;
import com.xo.backend.database.service.BookingTierAttributeService;
import com.xo.backend.database.service.EventAttributeService;
import com.xo.backend.database.service.PaymentService;
import com.xo.backend.database.service.VenueService;
import com.xo.backend.error.exceptions.*;
import com.xo.backend.mappers.BookingItemMapper;
import com.xo.backend.mappers.BookingListMapper;
import com.xo.backend.mappers.BookingMapper;
import com.xo.backend.model.dto.*;
import com.xo.backend.model.dto.go.GoUserProfileResponseDTO;
import com.xo.backend.model.dto.go.VenueDTO;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import com.xo.backend.model.dto.request.booking.AddItemsToBookingRequest;
import com.xo.backend.model.dto.request.booking.AdditionalBookingInfoRequest;
import com.xo.backend.model.dto.request.booking.BookingStatusUpdateResponseDTO;
import com.xo.backend.model.dto.request.booking.CreateBookingRequest;
import com.xo.backend.model.dto.responses.*;
import com.xo.backend.model.dto.responses.venueresponse.Coordinates;
import com.xo.backend.utlis.BookingHelper;
import com.xo.backend.utlis.BookingReferenceGenerator;
import com.xo.backend.utlis.SecurityContextUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.xo.backend.database.entity.bookings.BookingStatus.*;
import static com.xo.backend.database.entity.bookings.BookingTierType.RESERVATION;
import static com.xo.backend.database.entity.bookings.BookingTierType.TICKET;
import static com.xo.backend.utlis.Constants.*;
import static com.xo.backend.utlis.NullSafeUtils.setIfNotNull;
import static com.xo.backend.utlis.VenueUtils.safeParseVenueId;

@Service
@RequiredArgsConstructor
@Slf4j
public class BookingService {

    private final BookingRepository bookingRepository;
    private final VenueInfoService venueInfoService;
    private final UserInfoService userInfoService;
    private final BookingMapper bookingMapper;
    private final EventRepository eventRepository;
    private final EmailService emailService;
    private final BookingTierRepository bookingTierRepository;
    private final BookingTierAttributeService bookingTierAttributeService;
    private final EntryPassRepository entryPassRepository;
    private final EventAttributeService eventAttributeService;
    private final BookingItemMapper bookingItemMapper;
    private final BookingHelper bookingHelper;
    private final EntryPassService entryPassService;
    private final VenueService venueService;
    private final PaymentTransactionRepository paymentTransactionRepository;
    private final PaymentService paymentService;
    private final BookingListMapper bookingListMapper;


    private boolean isBookingNotDraftStatus(BookingEntity bookingEntity) {
        return bookingEntity.getStatus() != DRAFT;
    }


    @Transactional
    public CreateBookingResponseDTO createNewBooking(CreateBookingRequest createBookingRequest) {
        int userId = createBookingRequest.userId();
        int eventId = createBookingRequest.eventId();
        GoUserProfileResponseDTO goUserProfileResponseDTO = userInfoService.getUserProfile();

        Optional<BookingEntity> existingDraftBooking = bookingRepository.findByOwnerIdAndEventIdAndStatus(userId,
                eventId, DRAFT);

        if (existingDraftBooking.isPresent()) {
            return CreateBookingResponseDTO.builder()
                    .booking(bookingMapper.mapToBookingDTO(existingDraftBooking.get()))
                    .build();
        }

        EventEntity event=eventRepository.findById(eventId).orElse(null);
        // Validate the booking deadline not exceeded
        validateBookingDeadline(event);

        // Create a new booking (with a new ID)
        BookingEntity newBooking = bookingRepository.save(prepareDraftBookingEntity(userId, event, goUserProfileResponseDTO));

        return CreateBookingResponseDTO.builder().booking(bookingMapper.mapToBookingDTO(newBooking)).build();
    }

    private void validateBookingDeadline(EventEntity event) {
        if(event !=null && event.getBookingDeadline()!=null && Instant.now().isAfter(event.getBookingDeadline())) {
            throw new BookingDeadlineExceededException("Booking deadline exceeded");
        }
    }

    private BookingEntity prepareDraftBookingEntity(int userId, EventEntity event, GoUserProfileResponseDTO goUserProfileResponseDTO) {
        String creatorName = String.join(" ", goUserProfileResponseDTO.firstName(), goUserProfileResponseDTO.lastName());
        return BookingEntity.builder()
                .ownerId(userId)
                .eventId(Optional.ofNullable(event).map(EventEntity::getId).orElse(null))
                .bookingChannel(BookingChannel.USER_APP)
                .bookingNumber(BookingReferenceGenerator.generateReferenceNumber())
                .bookingName(String.join(" ",goUserProfileResponseDTO.firstName(),goUserProfileResponseDTO.lastName() ,"Booking"))
                .ownerName(creatorName)
                .creatorName(creatorName)
                .ownerEmail(goUserProfileResponseDTO.email())
                .ownerPhone(goUserProfileResponseDTO.phoneNumber())
                .event(event)
                .status(DRAFT)
                .paymentStatus(PaymentStatus.NOT_PAID)
                .build();
    }

    @Transactional
    public CreateBookingResponseDTO addItemsToBooking(Integer bookingId,
                                                      AddItemsToBookingRequest createBookingRequest) {
        // find booking Entity
        BookingEntity bookingEntity = bookingRepository.findById(bookingId).orElseThrow(BookingNotFoundException::new);

        // Validate the booking deadline not exceeded
        validateBookingDeadline(bookingEntity.getEvent());

        //only add items to draft bookings
        if (isBookingNotDraftStatus(bookingEntity)) {
            throw new InvalidBookingStatusException("cannot add items to booking if not draft");
        }

        if (paymentTransactionRepository.existsByBooking_IdAndTransactionStatus(bookingEntity.getId(), TransactionStatus.PENDING)) {
            paymentService.expireCheckoutSession(bookingId);
            throw new BookingPaymentSessionExistsException("There is an active Payment session for the payment , " +
                    "hence expiring the session, you can try again");
        }

        //check booking does not contain mixed items
        BookingType bookingType = bookingHelper.getBookingType(createBookingRequest.bookingItems());

        bookingHelper.validateUserBooking(bookingType, createBookingRequest.bookingItems());

        EventEntity eventEntity =
                eventRepository.findById(bookingEntity.getEventId()).orElseThrow(EventNotFoundException::new);
        Instant bookingDeadline = eventEntity.getBookingDeadline();

        if (bookingDeadline != null && bookingDeadline.isBefore(Instant.now())) {
            throw new BookingRequestException("booking deadline has passed");
        }

        // create booking items
        List<BookingItemEntity> bookingItemEntityList =
                bookingHelper.prepareBookingItems(createBookingRequest.bookingItems(),
                        bookingEntity, eventEntity.getId(), false);

        //replace booking items
        bookingEntity.getBookingItems().clear();
        bookingEntity.getBookingItems().addAll(bookingItemEntityList);

        bookingEntity.setUpdatedAt(Instant.now());
        bookingEntity.setBookingType(bookingType);

        bookingHelper.calculateBookingPaymentAmount(bookingEntity, bookingItemEntityList);
        bookingEntity.setRequireConfirmation(bookingHelper.isBookingConfirmationRequired(bookingEntity));

        BookingEntity updatedBooking = bookingRepository.save(bookingEntity);

        return CreateBookingResponseDTO.builder().booking(bookingMapper.mapToBookingDTO(updatedBooking)).build();
    }

    public CreateBookingResponseDTO addInfoToBooking(Integer bookingId,
                                                     AdditionalBookingInfoRequest additionalBookingInfoRequest) {

        BookingEntity bookingEntity = bookingRepository.findById(bookingId).orElseThrow(BookingNotFoundException::new);

        setIfNotNull(additionalBookingInfoRequest.comment(), bookingEntity::setComment);
        setIfNotNull(additionalBookingInfoRequest.bookingOccasion(), bookingEntity::setBookingOccasion);

        BookingEntity updatedBooking = bookingRepository.save(bookingEntity);

        return CreateBookingResponseDTO.builder().booking(bookingMapper.mapToBookingDTO(updatedBooking)).build();
    }

    @Transactional(readOnly = true)
    public TicketSummaryResponseDTO getTicketSummary(Integer bookingId) {
        BookingEntity booking = bookingRepository.findById(bookingId)
                .orElseThrow(BookingNotFoundException::new);

        if(bookingHelper.hasPreSaleDiscountsExpired(booking)){
            throw new PreSaleDiscountEndedException("Pre sale discounts have ended for this booking");
        }

        if (isBookingNotDraftStatus(booking)) {
            throw new InvalidBookingStatusException("Ticket summary is only available for draft bookings");
        }

        // Check if all booking items are of type TICKET
        boolean allTickets = booking.getBookingItems().stream()
                .allMatch(item -> {
                    BookingTierEntity tier = bookingTierRepository.findById(item.getBookingTierId())
                            .orElseThrow(BookingTierNotFoundException::new);
                    return tier.getType() == TICKET;
                });

        if (!allTickets) {
            throw new GeneralException("Ticket summary is only available for bookings with all items of type TICKET",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }

        List<BookingItemSummaryDTO> itemSummaries = booking.getBookingItems().stream()
                .map(this::mapToBookingItemSummaryDTO)
                .toList();

        VenueDTO venueDTO = venueInfoService.getVenueDetails(booking.getEvent().getVenueId());

        return TicketSummaryResponseDTO.builder()
                .items(itemSummaries)
                .subtotalPrice(booking.getNetAmount())
                .fees(booking.getTotalFee())
                .applicationFee(booking.getApplicationFee())
                .pspFee(booking.getPspFee())
                .totalPrice(booking.getTotalAmount())
                .currencyIso(venueDTO.currency().isoCode())
                .build();
    }

    /**
     * This method will recalculate the booking in case sale ended and , it will also remove booking items booking tier is not available
     * @param booking
     * @return boolean if price changed or any booking item removed
     */
    private BookingValidationDTO recalculateBooking(BookingEntity booking) {
        Map<Integer, BookingTierEntity> tierCache = bookingTierRepository
                .findAllById(
                        booking.getBookingItems()
                                .stream()
                                .map(BookingItemEntity::getBookingTierId)
                                .collect(Collectors.toSet()))
                .stream().collect(Collectors.toMap(BookingTierEntity::getId, Function.identity()));

        BigDecimal netAmount = BigDecimal.ZERO;
        Iterator<BookingItemEntity> bookingItemEntityIterator=booking.getBookingItems().iterator();

        BigDecimal priceBeforeRecalculation=booking.getTotalAmount()!=null?booking.getTotalAmount():BigDecimal.ZERO;
        boolean isPreSaleDiscountAvailable=bookingHelper.isPreSaleDiscountAvailable(booking);
        Set<BookingChangeReason> changeReasons = EnumSet.noneOf(BookingChangeReason.class);
        while (bookingItemEntityIterator.hasNext()) {
            BookingItemEntity bookingItemEntity = bookingItemEntityIterator.next();
            BookingTierEntity bookingTier = Optional.ofNullable(tierCache.get(bookingItemEntity.getBookingTierId()))
                    .orElseThrow(BookingTierNotFoundException::new);
            if ( bookingTier.getAvailableQty() < bookingItemEntity.getQuantity() || (  bookingTier.getType() == RESERVATION && !Range.closed(bookingTier.getMinPersons(), bookingTier.getMaxPersons() * bookingItemEntity.getQuantity()).contains(bookingItemEntity.getNumOfPeople()))) {
                bookingItemEntityIterator.remove();
                changeReasons.add(BookingChangeReason.ITEM_REMOVED);
                continue;
            }
            BigDecimal price = isPreSaleDiscountAvailable? bookingTier.getPreSaleDiscountedPrice():bookingTier.getPayOnlinePrice();
            BigDecimal totalPrice = price.multiply(BigDecimal.valueOf(bookingItemEntity.getQuantity()));
            bookingItemEntity.setPurchasePrice(price);
            bookingItemEntity.setTotalPrice(totalPrice);
            netAmount = netAmount.add(totalPrice);
        }

        booking.setNetAmount(netAmount);
        bookingHelper.updateBookingFees(booking);
        booking.setTotalAmount(netAmount.add(booking.getTotalFee()));
        booking.setHasPreSaleDiscounts(isPreSaleDiscountAvailable && !booking.getBookingItems().isEmpty());
        if (priceBeforeRecalculation.compareTo(booking.getTotalAmount()) != 0) {
            changeReasons.add(BookingChangeReason.PRICE_CHANGED);
        }
        return BookingValidationDTO.builder().isBookingChanged(!changeReasons.isEmpty()).changeReasons(changeReasons).build();
    }

    private BookingItemSummaryDTO mapToBookingItemSummaryDTO(BookingItemEntity item) {
        BookingTierEntity tier = bookingTierRepository.findById(item.getBookingTierId())
                .orElseThrow(BookingTierNotFoundException::new);

        String name = getBookingTierAttributeValue(tier, "tier-name");
        String consumptionText = getBookingTierAttributeValue(tier, "included-consumption-description");

        return BookingItemSummaryDTO.builder()
                .name(name)
                .quantity(item.getQuantity())
                .consumptionText(consumptionText)
                .priceEach(item.getPurchasePrice())
                .build();
    }

    private String getBookingTierAttributeValue(BookingTierEntity tier, String attributeName) {
        BookingTierAttributeEntity attribute = bookingTierAttributeService
                .getBookingTierAttributeByName(tier.getBookingTierAttributes(), attributeName);
        return attribute != null ? attribute.getAttributeValue() : "";
    }

    private String getEventAttributeValue(EventEntity event, String attributeName) {
        EventAttributeEntity attribute = eventAttributeService
                .getEventAttributeByName(event.getEventAttributes(), attributeName);
        return attribute != null ? attribute.getAttributeValue() : "";
    }

    @Transactional(readOnly = true)
    public ReservationSummaryResponseDTO getReservationSummary(Integer bookingId) {
        BookingEntity booking = bookingRepository.findById(bookingId)
                .orElseThrow(BookingNotFoundException::new);

        if(bookingHelper.hasPreSaleDiscountsExpired(booking)){
            throw new PreSaleDiscountEndedException("Pre sale discounts have ended for this booking");
        }

        if (isBookingNotDraftStatus(booking)) {
            throw new InvalidBookingStatusException("Reservation summary is only available for draft bookings");
        }

        if (booking.getBookingItems().size() != 1) {
            throw new GeneralException("Reservation booking should have exactly one item",
                    HttpStatus.INTERNAL_SERVER_ERROR);
        }

        BookingItemEntity reservationItem = booking.getBookingItems().getFirst();
        BookingTierEntity bookingTier = bookingTierRepository.findById(reservationItem.getBookingTierId())
                .orElseThrow(BookingTierNotFoundException::new);

        if (bookingTier.getType() != BookingTierType.RESERVATION) {
            throw new GeneralException("Booking tier is not a reservation type", HttpStatus.INTERNAL_SERVER_ERROR);
        }

        VenueDTO venueDTO = venueInfoService.getVenueDetails(booking.getEvent().getVenueId());

        ReservationItemSummaryDTO itemSummary = mapToReservationItemSummaryDTO(reservationItem, bookingTier);

        return ReservationSummaryResponseDTO.builder()
                .reservationItem(itemSummary)
                .bookingName(booking.getBookingName())
                .comment(booking.getComment())
                .occasion(booking.getBookingOccasion())
                .subtotalPrice(booking.getNetAmount())
                .fees(booking.getTotalFee())
                .applicationFee(booking.getApplicationFee())
                .pspFee(booking.getPspFee())
                .totalPrice(booking.getTotalAmount())
                .currencyIso(venueDTO.currency().isoCode())
                .build();
    }

    private ReservationItemSummaryDTO mapToReservationItemSummaryDTO(BookingItemEntity item, BookingTierEntity tier) {
        String name = getBookingTierAttributeValue(tier, "tier-name");
        String includedConsumptionAmount = getBookingTierAttributeValue(tier, "included-consumption-amount");

        BookingTierAttributeEntity attribute =
                bookingTierAttributeService.getBookingTierAttributeByName(tier.getBookingTierAttributes(), "minimum" +
                        "-spent");

        BigDecimal minimumSpent = attribute != null ?
                new BigDecimal(attribute.getAttributeValue()) :
                BigDecimal.ZERO;

        return ReservationItemSummaryDTO.builder()
                .name(name)
                .quantity(item.getQuantity())
                .numberOfPeople(item.getNumOfPeople())
                .minimumSpent(minimumSpent)
                .includedConsumptionAmount(includedConsumptionAmount)
                .minPeople(tier.getMinPersons())
                .maxPeople(tier.getMaxPersons())
                .build();
    }

    @Transactional
    public BookingStatusUpdateResponseDTO finalizeBooking(Integer bookingId) {

        BookingEntity booking = bookingRepository.findById(bookingId)
                .orElseThrow(BookingNotFoundException::new);

        if (isBookingNotDraftStatus(booking)) {
            throw new InvalidBookingStatusException("Only draft bookings can be finalized");
        }

        // Validate the booking deadline not exceeded
        validateBookingDeadline(booking.getEvent());

        boolean bookingConfirmationRequirement = bookingHelper.isBookingConfirmationRequired(booking.getRequireConfirmation());

        if (bookingConfirmationRequirement) {
            booking.setStatus(PENDING);
        } else {
            validatePayment(booking);
            booking.setStatus(CONFIRMED);
            entryPassService.createEntryPassesAndUpdateAvailability(booking);
        }
        booking.setSubmittedAt(Instant.now());
        bookingRepository.save(booking);

        emailService.sendBookingEmail(booking);
        return BookingStatusUpdateResponseDTO.builder().bookingId(bookingId).status(booking.getStatus()).build();
    }


    private void validatePayment(BookingEntity booking) {
        boolean requiresPayment = booking.getTotalAmount().compareTo(BigDecimal.ZERO) > 0;
        if (requiresPayment && !isBookingPaid(booking)) {
            throw new PaymentRequiredException("Payment is required before confirming the booking");
        }
    }

    public boolean isBookingPaid(BookingEntity booking) {
        return booking.getPaymentStatus() == PaymentStatus.PAID;
    }

    @Transactional(readOnly = true)
    public DetailedBookingDTO getBookingDetails(int bookingId) {

        BookingEntity booking = bookingRepository.findById(bookingId).orElseThrow(BookingNotFoundException::new);

        EventEntity event = eventRepository.findById(booking.getEventId()).orElseThrow(EventNotFoundException::new);

        String eventTitle = getEventAttributeValue(event, TITLE);
        String eventBanner = getEventAttributeValue(event, BANNER);

        boolean isBookingPast = event.getEndDatetime().isBefore(Instant.now());

        VenueDTO venueDTO=venueInfoService.getVenueDetails(event.getVenueId());
        AddressDTO addressDTO = venueService.getVenueAddressDetails(event,venueDTO);

        List<EntryPassDTO> entryPassDTOs = createEntryPassDTOList(booking, event, eventTitle, addressDTO);

        if (!isValidBookingStatus(booking.getStatus())) {
            throw new InvalidBookingStatusException("Booking status must be confirmed or arrived.");
        }
        return DetailedBookingDTO.builder()
                .id(booking.getId())
                .eventBanner(eventBanner)
                .eventTitle(eventTitle)
                .eventDateTime(OffsetDateTime.ofInstant(event.getStartDatetime(), ZoneOffset.UTC))
                .venueName(addressDTO.addressName())
                .bookingType(booking.getBookingType())
                .numberOfEntryPasses(entryPassDTOs.size())
                .status(isBookingPast ? PAST.toString() : booking.getStatus().toString())
                .ownerId(booking.getOwnerId())
                .ownerName(booking.getOwnerName())
                .bookingName(booking.getBookingName())
                .bookingNumber(booking.getBookingNumber())
                .timeZone(venueDTO.timeZone())
                .entryPasses(entryPassDTOs)
                .totalFee(booking.getTotalFee())
                .netAmount(booking.getNetAmount())
                .totalAmount(booking.getTotalAmount())
                .currencyIso(venueDTO.currency().isoCode())
                .bookingItems(booking.getBookingItems().stream().map(bookingItemMapper::mapToBookingItemDTO).toList())
                .paymentStatus(booking.getPaymentStatus())
                .bookingOccasion(booking.getBookingOccasion())
                .comment(booking.getComment())
                .coordinates(Coordinates.builder().latitude(addressDTO.latitude()).longitude(addressDTO.longitude()).build())
                .build();
    }

    private boolean isValidBookingStatus(BookingStatus status) {
        return status == CONFIRMED || status == ARRIVED;
    }

    private List<EntryPassDTO> createEntryPassDTOList(BookingEntity booking, EventEntity event, String eventTitle,
                                                      AddressDTO addressDTO) {

        List<EntryPassEntity> entryPasses = entryPassRepository.findEntryPassEntitiesByBooking(booking);

        if (entryPasses.isEmpty()) {
            return Collections.emptyList();
        }

        List<EntryPassDTO> entryPassDTOs = new ArrayList<>();
        Map<Integer, BookingTierAttrForEntryPassDTO> bookingTierAttrMap = new HashMap<>();

        for (EntryPassEntity entryPass : entryPasses) {

            UUID entryPassId = entryPass.getId();
            byte[] qrCodeImage;

            try {
                qrCodeImage = QrCodeService.generateQRCode(String.valueOf(entryPassId));
            } catch (IOException | WriterException e) {
                log.error("Error generating QR code image for entry pass: {}", entryPassId, e);
                throw new GeneralException("Error generating QR code image for entry pass: " + entryPassId,
                        HttpStatus.INTERNAL_SERVER_ERROR);
            }

            String encodedQrCode = Base64.getEncoder().encodeToString(qrCodeImage);

            BookingTierEntity bookingTier =
                    Optional.ofNullable(entryPass.getBookingTier()).orElseThrow(BookingTierNotFoundException::new);

            BookingTierAttrForEntryPassDTO bookingTierData = bookingTierAttrMap.computeIfAbsent(bookingTier.getId(),
                    k -> createBookingTierAttrForEntryPassDTO(bookingTier));

            EntryPassDTO entryPassDTO = EntryPassDTO.builder()
                    .entryPassId(entryPassId)
                    .bookingTierName(bookingTierData.tierName())
                    .qrCode(encodedQrCode)
                    .venueName(addressDTO.addressName())
                    .eventName(eventTitle)
                    .entryPassReferenceNumber(entryPass.getReferenceNumber())
                    .shownPrice(entryPass.getPrice())
                    .bookedByFullName(booking.getOwnerName())
                    .bookingNumber(booking.getBookingNumber())
                    .eventStartTime(OffsetDateTime.ofInstant(event.getStartDatetime(), ZoneOffset.UTC))
                    .entryPassDescription(bookingTierData.description())
                    .entryPassIncludedConsumptionAmount(bookingTierData.consumptionAmountValue())
                    .entryPassIncludedConsumptionText(bookingTierData.consumptionTextValue())
                    .used(entryPass.getUsed())
                    .build();

            entryPassDTOs.add(entryPassDTO);
        }
        return entryPassDTOs;
    }

    private BookingTierAttrForEntryPassDTO createBookingTierAttrForEntryPassDTO(BookingTierEntity bookingTier) {

        return BookingTierAttrForEntryPassDTO.builder()
                .tierName(getBookingTierAttributeValue(bookingTier, TIER_NAME))
                .consumptionTextValue(getBookingTierAttributeValue(bookingTier, INCLUDED_CONSUMPTION_DESCRIPTION))
                .consumptionAmountValue(getBookingTierAttributeValue(bookingTier, INCLUDED_CONSUMPTION_AMOUNT))
                .description(getBookingTierAttributeValue(bookingTier, DESCRIPTION))
                .build();
    }

    @Deprecated
    @Transactional(readOnly = true)
    public Page<BookingListResponse> getAllBookingsForUser(Pageable pageable) {

        Integer userId = SecurityContextUtil.getCurrentUserId();

        List<BookingStatus> statuses = List.of(PENDING, APPROVED, CONFIRMED, ARRIVED);

        Page<BookingEntity> bookings = bookingRepository.findBookingEntitiesByOwnerIdAndStatusIn(userId, statuses, pageable);

        Set<Long> venueIdSet = bookings.stream()
                .map(booking -> safeParseVenueId(booking.getEvent().getVenueId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Map<Long, VenueOverviewDTO> venueMap = venueInfoService.getVenuesOverview(venueIdSet);

        return bookings.map(booking -> {
            Long venueId = safeParseVenueId(booking.getEvent().getVenueId());
            String venueName = venueId != null
                    ? Optional.ofNullable(venueMap.get(venueId))
                    .map(VenueOverviewDTO::name)
                    .orElse(null)
                    : null;

            return convertToDTO(booking, venueName);
        });
    }

    @Deprecated
    private BookingListResponse convertToDTO(BookingEntity booking, String venueName) {

        int eventId = booking.getEventId();
        EventEntity event = eventRepository.findById(eventId).orElseThrow(EventNotFoundException::new);
        String eventTitle = getEventAttributeValue(event, TITLE);
        String eventBanner = getEventAttributeValue(event, BANNER);

        boolean isBookingPast = event.getEndDatetime().isBefore(Instant.now());

        return BookingListResponse.builder()
                .bookingId(booking.getId())
                .eventId(eventId)
                .eventStatus(event.getStatus())
                .eventBanner(eventBanner)
                .eventTitle(eventTitle)
                .bookingName(booking.getBookingName())
                .eventDateTime(OffsetDateTime.ofInstant(event.getStartDatetime(), ZoneOffset.UTC))
                .venueName(venueName)
                .status(isBookingPast ? PAST.toString() : booking.getStatus().toString())
                .bookingType(booking.getBookingType())
                .build();
    }

    @Transactional(readOnly = true)
    public Page<BookingListResponse> getUpcomingBookingsForUser(Pageable pageable) {
        Integer userId = SecurityContextUtil.getCurrentUserId();
        Instant now = Instant.now();

        List<BookingStatus> upcomingStatuses = Arrays.asList(PENDING, APPROVED, CANCELLED, CONFIRMED, ARRIVED);

        Page<BookingEntity> bookings = bookingRepository.findUpcomingBookings(userId, now, upcomingStatuses, pageable);

        return mapToBookingListResponse(bookings);
    }

    @Transactional(readOnly = true)
    public Page<BookingListResponse> getCompletedBookingsForUser(Pageable pageable) {
        Integer userId = SecurityContextUtil.getCurrentUserId();
        Instant now = Instant.now();

        List<BookingStatus> completedStatuses = Arrays.asList(CONFIRMED, ARRIVED);

        Page<BookingEntity> bookings = bookingRepository.findCompletedBookings(userId, now, completedStatuses, pageable);

        return mapToBookingListResponse(bookings);
    }

    private Page<BookingListResponse> mapToBookingListResponse(Page<BookingEntity> bookings) {
        return bookings.map(booking -> {
            VenueDTO venueDTO=venueInfoService.getVenueDetails(booking.getEvent().getVenueId());
            boolean hasEntryPasses = entryPassRepository.existsByBookingId(booking.getId());
            AddressDTO addressDTO = venueService.getVenueAddressDetails(booking.getEvent(),venueDTO);
            return bookingListMapper.toBookingListResponse(booking, addressDTO, hasEntryPasses,venueDTO);
        });
    }

    @Transactional
    public PaymentStatus getBookingPaymentStatus(Integer bookingId) {
        BookingEntity booking = bookingRepository.findById(bookingId).orElseThrow(BookingNotFoundException::new);
        return booking.getPaymentStatus();
    }

    @Transactional
    public void expireBookingPaymentSessions(Integer bookingId){
        paymentService.expireCheckoutSession(bookingId);
    }

    @Transactional
    public BookingValidationDTO validateBooking(Integer bookingId) {
        BookingEntity booking=bookingRepository.findById(bookingId).orElseThrow(BookingNotFoundException::new);
        if(DRAFT==booking.getStatus()) {
            return recalculateBooking(booking);
        }
        return BookingValidationDTO.builder().isBookingChanged(false).build();
    }
}

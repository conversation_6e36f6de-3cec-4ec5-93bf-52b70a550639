package com.xo.backend.service;

import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.bookings.BookingItemEntity;
import com.xo.backend.database.entity.bookings.BookingTierType;
import com.xo.backend.database.entity.fees.FeeConfigEntity;
import com.xo.backend.database.repository.fees.FeeConfigRepository;
import com.xo.backend.model.dto.fees.BookingFeeDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;

@Service
@Slf4j
@RequiredArgsConstructor
public class FeeCalculationService {

    private static final String DEFAULT_CONFIG_ID = "DEFAULT";

    private final FeeConfigRepository feeConfigRepository;

    public BookingFeeDTO calculateBookingFees(BookingEntity bookingEntity) {
        String venueId = bookingEntity.getEvent().getVenueId();

        FeeConfigEntity feeConfig = feeConfigRepository.findByVenueId(venueId)
                .orElseGet(this::getDefaultFeeConfig);

        BigDecimal applicationFee = calculateApplicationFee(bookingEntity, feeConfig);
        BigDecimal pspFee = calculatePspFee(bookingEntity, applicationFee,feeConfig);

        return BookingFeeDTO.builder()
                .applicationFee(applicationFee)
                .pspFee(pspFee)
                .totalFee(applicationFee.add(pspFee))
                .build();
    }

    private FeeConfigEntity getDefaultFeeConfig() {
        log.info("No fee config found for venue, using default");
        return feeConfigRepository.findByVenueId(DEFAULT_CONFIG_ID).orElseThrow(() -> new IllegalStateException("No default fee config found"));
    }

    private BigDecimal calculateApplicationFee(BookingEntity bookingEntity, FeeConfigEntity feeConfig) {

        BigDecimal calculatedFee = feeConfig.getAppFeePerc()
                .multiply(bookingEntity.getNetAmount())
                .add(feeConfig.getAppFeeFixedPerTransaction())
                .add(feeConfig.getAppFeeFixedPerEntryPass().multiply(BigDecimal.valueOf(calculateNumberOfEntryPasses(bookingEntity))))
                .multiply(feeConfig.getAppFeeToCustomerPerc());

        // Apply min/max limits if specified
        if (feeConfig.getAppFeeMin() != null) {
            log.info("Applying min fee limit of {} on initial fee of {}", feeConfig.getAppFeeMin(), calculatedFee);
            calculatedFee = calculatedFee.max(feeConfig.getAppFeeMin());
        }
        if (feeConfig.getAppFeeMax() != null) {
            log.info("Applying max fee limit of {} on initial fee of {}", feeConfig.getAppFeeMax(), calculatedFee);
            calculatedFee = calculatedFee.min(feeConfig.getAppFeeMax());
        }

        return calculatedFee;
    }

    private BigDecimal calculatePspFee(BookingEntity bookingEntity, BigDecimal applicationFee, FeeConfigEntity feeConfig) {

        // Venue wants to absorb psp fees + additional fee caused by platform fee
        if(Boolean.FALSE.equals(feeConfig.getPassPspFees())) {
            return BigDecimal.ZERO;
        }

        // PspFeeOffset = % * (NetAmount + ApplicationFee) + FixedAmount / (1 - %)
        return feeConfig.getPspFeePerc()
                .multiply(bookingEntity.getNetAmount().add(applicationFee))
                .add(feeConfig.getPspFeeFixed())
                .divide(BigDecimal.ONE.subtract(feeConfig.getPspFeePerc()), 2, RoundingMode.HALF_UP);
    }

    private Integer calculateNumberOfEntryPasses(BookingEntity bookingEntity) {

        int numberOfTicketEntryPasses = bookingEntity.getBookingItems().stream()
                .filter(bookingItemEntity -> bookingItemEntity.getBookingTier().getType() == BookingTierType.TICKET)
                .mapToInt(BookingItemEntity::getQuantity)
                .sum();

        int numberOfReservationEntryPasses = bookingEntity.getBookingItems().stream()
                .filter(bookingItemEntity -> bookingItemEntity.getBookingTier().getType() == BookingTierType.RESERVATION)
                .mapToInt(BookingItemEntity::getNumOfPeople)
                .sum();

        return numberOfTicketEntryPasses + numberOfReservationEntryPasses;
    }


}

databaseChangeLog:
  - changeSet:
      id: 49
      author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
      preConditions:
        - onFail: MARK_RAN
        - tableExists:
            tableName: venue_fee_config
            schemaName: xo
        - sqlCheck:
            expectedResult: 0
            sql: SELECT COUNT(*) FROM xo.venue_fee_config WHERE venue_id = 'DEFAULT'
      changes:
        - insert:
            tableName: venue_fee_config
            schemaName: xo
            columns:
              - column:
                  name: venue_id
                  value: "DEFAULT"
              - column:
                  name: app_fee_perc
                  valueNumeric:  0.00
              - column:
                  name: app_fee_fixed_per_transaction
                  valueNumeric: 1.00
              - column:
                  name: app_fee_fixed_per_entry_pass
                  valueNumeric: 1.50
              - column:
                  name: app_fee_to_customer_perc
                  valueNumeric: 1.00
              - column:
                  name: app_fee_min
                  valueNumeric: 1.50
              - column:
                  name: app_fee_max
                  valueNumeric: 25.00
              - column:
                  name: psp_fee_perc
                  valueNumeric: 0.00
              - column:
                  name: psp_fee_fixed
                  valueNumeric: 0.00
              - column:
                  name: pass_psp_fees
                  valueBoolean: false
              - column:
                  name: created_at
                  valueDate: now()
              - column:
                  name: updated_at
                  valueDate: now()
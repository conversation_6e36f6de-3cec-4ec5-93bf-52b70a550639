databaseChangeLog:
  - changeSet:
      id: 50
      author: <PERSON><PERSON><PERSON>_<PERSON><PERSON>ris
      preConditions:
        onFail: MARK_RAN
        and:
          - tableExists:
              tableName: venue_fee_config
              schemaName: xo
          - tableExists:
              tableName: market_place_venue_config
              schemaName: xo
          - not:
              foreignKeyConstraintExists:
                foreignKeyName: fk_venue_fee_config_venue_id
                baseTableName: venue_fee_config
                baseTableSchemaName: xo
      changes:
        - addForeignKeyConstraint:
            baseTableName: venue_fee_config
            baseTableSchemaName: xo
            baseColumnNames: venue_id
            constraintName: fk_venue_fee_config_venue_id
            referencedTableName: market_place_venue_config
            referencedTableSchemaName: xo
            referencedColumnNames: venue_id
            onDelete: CASCADE
            onUpdate: CASCADE

package com.xo.backend.service;

import com.xo.backend.database.entity.bookings.BookingEntity;
import com.xo.backend.database.entity.bookings.BookingItemEntity;
import com.xo.backend.database.entity.bookings.BookingTierEntity;
import com.xo.backend.database.entity.bookings.BookingTierType;
import com.xo.backend.database.entity.events.EventEntity;
import com.xo.backend.database.entity.fees.FeeConfigEntity;
import com.xo.backend.database.repository.fees.FeeConfigRepository;
import com.xo.backend.model.dto.fees.BookingFeeDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class FeeCalculationServiceTest {

    @Mock
    private FeeConfigRepository feeConfigRepository;

    @InjectMocks
    private FeeCalculationService feeCalculationService;

    private BookingEntity bookingEntity;
    private FeeConfigEntity feeConfigEntity;
    private FeeConfigEntity defaultFeeConfigEntity;

    @BeforeEach
    void setUp() {
        // Setup event entity
        EventEntity eventEntity = EventEntity.builder()
                .id(1)
                .venueId("venue123")
                .build();

        // Setup booking entity
        bookingEntity = BookingEntity.builder()
                .id(1)
                .eventId(1)
                .event(eventEntity)
                .netAmount(new BigDecimal("100.00"))
                .build();

        // Setup fee config entity
        feeConfigEntity = FeeConfigEntity.builder()
                .id(1)
                .venueId("venue123")
                .appFeePerc(new BigDecimal("0.0500")) // 5%
                .appFeeFixedPerTransaction(new BigDecimal("2.00"))
                .appFeeFixedPerEntryPass(new BigDecimal("1.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.0000")) // 100%
                .pspFeePerc(new BigDecimal("0.0290")) // 2.9%
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(true)
                .build();

        // Setup default fee config entity
        defaultFeeConfigEntity = FeeConfigEntity.builder()
                .id(2)
                .venueId("default")
                .appFeePerc(new BigDecimal("0.0300")) // 3%
                .appFeeFixedPerTransaction(new BigDecimal("1.50"))
                .appFeeFixedPerEntryPass(new BigDecimal("0.50"))
                .appFeeToCustomerPerc(new BigDecimal("1.0000")) // 100%
                .pspFeePerc(new BigDecimal("0.0290")) // 2.9%
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(true)
                .build();
    }

    @Test
    void calculateBookingFees_WithVenueSpecificConfig_ShouldUseVenueConfig() {
        // Given
        setupBookingWithTicketItems(2); // 2 tickets
        when(feeConfigRepository.findByVenueId("venue123")).thenReturn(Optional.of(feeConfigEntity));

        // When
        BookingFeeDTO result = feeCalculationService.calculateBookingFees(bookingEntity);

        // Then
        assertNotNull(result);

        // Application fee calculation: (5% * 100) + 2.00 + (1.00 * 2) = 5 + 2 + 2 = 9.00
        assertEquals(0, new BigDecimal("9.00").compareTo(result.applicationFee()));

        // PSP fee calculation: (2.9% * (100 + 9) + 0.30) / (1 - 2.9%) = (3.161 + 0.30) / 0.971 = 3.56
        assertEquals(0, new BigDecimal("3.56").compareTo(result.pspFee()));

        // Total fee: 9.00 + 3.56 = 12.56
        assertEquals(0, new BigDecimal("12.56").compareTo(result.totalFee()));
    }

    @Test
    void calculateBookingFees_WithoutVenueConfig_ShouldUseDefaultConfig() {
        // Given
        setupBookingWithTicketItems(1); // 1 ticket
        when(feeConfigRepository.findByVenueId("venue123")).thenReturn(Optional.empty());
        when(feeConfigRepository.findByVenueId("DEFAULT")).thenReturn(Optional.of(defaultFeeConfigEntity));

        // When
        BookingFeeDTO result = feeCalculationService.calculateBookingFees(bookingEntity);

        // Then
        assertNotNull(result);

        // Application fee calculation: (3% * 100) + 1.50 + (0.50 * 1) = 3 + 1.5 + 0.5 = 5.00
        assertEquals(0, new BigDecimal("5.00").compareTo(result.applicationFee()));

        // PSP fee calculation: (2.9% * (100 + 5) + 0.30) / (1 - 2.9%) = (3.045 + 0.30) / 0.971 = 3.44
        assertEquals(0, new BigDecimal("3.44").compareTo(result.pspFee()));

        // Total fee: 5.00 + 3.44 = 8.44
        assertEquals(0, new BigDecimal("8.44").compareTo(result.totalFee()));
    }

    @Test
    void calculateBookingFees_WithNoDefaultConfig_ShouldThrowException() {
        // Given
        when(feeConfigRepository.findByVenueId("venue123")).thenReturn(Optional.empty());
        when(feeConfigRepository.findByVenueId("DEFAULT")).thenReturn(Optional.empty());

        // When & Then
        IllegalStateException exception = assertThrows(IllegalStateException.class,
            () -> feeCalculationService.calculateBookingFees(bookingEntity));

        assertEquals("No default fee config found", exception.getMessage());
    }

    @Test
    void calculateBookingFees_WithMinFeeLimit_ShouldApplyMinimum() {
        // Given
        feeConfigEntity.setAppFeeMin(new BigDecimal("15.00")); // Set minimum higher than calculated fee
        setupBookingWithTicketItems(1); // 1 ticket
        when(feeConfigRepository.findByVenueId("venue123")).thenReturn(Optional.of(feeConfigEntity));

        // When
        BookingFeeDTO result = feeCalculationService.calculateBookingFees(bookingEntity);

        // Then
        // Calculated fee would be: (5% * 100) + 2.00 + (1.00 * 1) = 8.00
        // But minimum is 15.00, so should use 15.00
        assertEquals(0, new BigDecimal("15.00").compareTo(result.applicationFee()));
    }

    @Test
    void calculateBookingFees_WithMaxFeeLimit_ShouldApplyMaximum() {
        // Given
        feeConfigEntity.setAppFeeMax(new BigDecimal("5.00")); // Set maximum lower than calculated fee
        setupBookingWithTicketItems(2); // 2 tickets
        when(feeConfigRepository.findByVenueId("venue123")).thenReturn(Optional.of(feeConfigEntity));

        // When
        BookingFeeDTO result = feeCalculationService.calculateBookingFees(bookingEntity);

        // Then
        // Calculated fee would be: (5% * 100) + 2.00 + (1.00 * 2) = 9.00
        // But maximum is 5.00, so should use 5.00
        assertEquals(0, new BigDecimal("5.00").compareTo(result.applicationFee()));
    }

    @Test
    void calculateBookingFees_WithPspFeesDisabled_ShouldReturnZeroPspFee() {
        // Given
        feeConfigEntity.setPassPspFees(false); // Venue absorbs PSP fees
        setupBookingWithTicketItems(1);
        when(feeConfigRepository.findByVenueId("venue123")).thenReturn(Optional.of(feeConfigEntity));

        // When
        BookingFeeDTO result = feeCalculationService.calculateBookingFees(bookingEntity);

        // Then
        assertEquals(BigDecimal.ZERO, result.pspFee());
        // Total fee should only be application fee
        assertEquals(result.applicationFee(), result.totalFee());
    }

    @Test
    void calculateBookingFees_WithReservationItems_ShouldCalculateCorrectEntryPasses() {
        // Given
        setupBookingWithReservationItems(2, 4); // 2 reservations with 4 people each
        when(feeConfigRepository.findByVenueId("venue123")).thenReturn(Optional.of(feeConfigEntity));

        // When
        BookingFeeDTO result = feeCalculationService.calculateBookingFees(bookingEntity);

        // Then
        // Application fee calculation: (5% * 100) + 2.00 + (1.00 * 8) = 5 + 2 + 8 = 15.00
        // 8 entry passes = 2 reservations * 4 people each
        assertEquals(0, new BigDecimal("15.00").compareTo(result.applicationFee()));
    }

    @Test
    void calculateBookingFees_WithMixedItems_ShouldCalculateCorrectEntryPasses() {
        // Given
        setupBookingWithMixedItems(); // 2 tickets + 1 reservation with 3 people
        when(feeConfigRepository.findByVenueId("venue123")).thenReturn(Optional.of(feeConfigEntity));

        // When
        BookingFeeDTO result = feeCalculationService.calculateBookingFees(bookingEntity);

        // Then
        // Application fee calculation: (5% * 100) + 2.00 + (1.00 * 5) = 5 + 2 + 5 = 12.00
        // 5 entry passes = 2 tickets + 3 people from reservation
        assertEquals(0, new BigDecimal("12.00").compareTo(result.applicationFee()));
    }

    @Test
    void calculateBookingFees_WithZeroNetAmount_ShouldCalculateBasedOnFixedFees() {
        // Given
        bookingEntity.setNetAmount(BigDecimal.ZERO);
        setupBookingWithTicketItems(1);
        when(feeConfigRepository.findByVenueId("venue123")).thenReturn(Optional.of(feeConfigEntity));

        // When
        BookingFeeDTO result = feeCalculationService.calculateBookingFees(bookingEntity);

        // Then
        // Application fee calculation: (5% * 0) + 2.00 + (1.00 * 1) = 0 + 2 + 1 = 3.00
        assertEquals(0, new BigDecimal("3.00").compareTo(result.applicationFee()));
    }

    private void setupBookingWithTicketItems(int numberOfTickets) {
        BookingTierEntity ticketTier = BookingTierEntity.builder()
                .id(1)
                .type(BookingTierType.TICKET)
                .build();

        BookingItemEntity ticketItem = BookingItemEntity.builder()
                .id(1)
                .quantity(numberOfTickets)
                .bookingTier(ticketTier)
                .build();

        bookingEntity.setBookingItems(List.of(ticketItem));
    }

    private void setupBookingWithReservationItems(int numberOfReservations, int peoplePerReservation) {
        BookingTierEntity reservationTier = BookingTierEntity.builder()
                .id(2)
                .type(BookingTierType.RESERVATION)
                .build();

        BookingItemEntity reservationItem = BookingItemEntity.builder()
                .id(2)
                .quantity(numberOfReservations)
                .numOfPeople(peoplePerReservation * numberOfReservations) // Total people across all reservations
                .bookingTier(reservationTier)
                .build();

        bookingEntity.setBookingItems(List.of(reservationItem));
    }

    private void setupBookingWithMixedItems() {
        BookingTierEntity ticketTier = BookingTierEntity.builder()
                .id(1)
                .type(BookingTierType.TICKET)
                .build();

        BookingTierEntity reservationTier = BookingTierEntity.builder()
                .id(2)
                .type(BookingTierType.RESERVATION)
                .build();

        BookingItemEntity ticketItem = BookingItemEntity.builder()
                .id(1)
                .quantity(2) // 2 tickets
                .bookingTier(ticketTier)
                .build();

        BookingItemEntity reservationItem = BookingItemEntity.builder()
                .id(2)
                .quantity(1) // 1 reservation
                .numOfPeople(3) // with 3 people
                .bookingTier(reservationTier)
                .build();

        bookingEntity.setBookingItems(List.of(ticketItem, reservationItem));
    }
}

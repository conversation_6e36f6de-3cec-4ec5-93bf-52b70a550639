package com.xo.marketplace.service;

import com.xo.backend.error.exceptions.MarketPlaceVenueConfigNotFoundException;
import com.xo.marketplace.database.MarketPlaceVenueConfig;
import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import com.xo.marketplace.mapper.MarketPlaceVenueConfigMapper;
import com.xo.marketplace.repository.MarketPlaceVenueConfigRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MarketPlaceVenueConfigServiceTest {

    @Mock
    private MarketPlaceVenueConfigRepository repository;

    @Mock
    private MarketPlaceVenueConfigMapper mapper;

    @InjectMocks
    private MarketPlaceVenueConfigService service;

    private static final String VALID_VENUE_ID = "venue-123";
    private static final String NONEXISTENT_VENUE_ID = "nonexistent-venue";
    private static final Integer VALID_CONFIG_ID = 1;

    private MarketPlaceVenueConfig venueConfig;
    private MarketPlaceVenueConfigDTO venueConfigDTO;

    @BeforeEach
    void setUp() {
        venueConfig = MarketPlaceVenueConfig.builder()
                .id(VALID_CONFIG_ID)
                .venueId(VALID_VENUE_ID)
                .visible(true)
                .build();

        venueConfigDTO = new MarketPlaceVenueConfigDTO(VALID_CONFIG_ID, VALID_VENUE_ID, true,"US");
    }

    @Test
    @DisplayName("getMarketPlaceVenueConfigByVenueId should return venue config when venue ID exists")
    void getMarketPlaceVenueConfigByVenueId_WhenVenueExists_ShouldReturnConfig() {
        // Arrange
        when(repository.findByVenueId(VALID_VENUE_ID)).thenReturn(Optional.of(venueConfig));
        when(mapper.toMarketPlaceVenueConfigDTO(venueConfig)).thenReturn(venueConfigDTO);

        // Act
        MarketPlaceVenueConfigDTO result = service.getMarketPlaceVenueConfigByVenueId(VALID_VENUE_ID);

        // Assert
        assertThat(result).isNotNull().isEqualTo(venueConfigDTO);
        verify(repository).findByVenueId(VALID_VENUE_ID);
        verify(mapper).toMarketPlaceVenueConfigDTO(venueConfig);
    }

    @Test
    @DisplayName("getMarketPlaceVenueConfigByVenueId should throw exception when venue ID does not exist")
    void getMarketPlaceVenueConfigByVenueId_WhenVenueDoesNotExist_ShouldThrowException() {
        // Arrange
        when(repository.findByVenueId(NONEXISTENT_VENUE_ID)).thenReturn(Optional.empty());

        // Act & Assert
        assertThatThrownBy(() -> service.getMarketPlaceVenueConfigByVenueId(NONEXISTENT_VENUE_ID))
                .isInstanceOf(MarketPlaceVenueConfigNotFoundException.class);
        verify(repository).findByVenueId(NONEXISTENT_VENUE_ID);
        verifyNoInteractions(mapper);
    }

    @Test
    @DisplayName("getAllMarketPlaceVenueConfigs should return all venue configs when data exists")
    void getAllMarketPlaceVenueConfigs_WhenDataExists_ShouldReturnAllConfigs() {
        // Arrange
        MarketPlaceVenueConfig config1 = MarketPlaceVenueConfig.builder()
                .id(1)
                .venueId("venue-1")
                .visible(true)
                .build();

        MarketPlaceVenueConfig config2 = MarketPlaceVenueConfig.builder()
                .id(2)
                .venueId("venue-2")
                .visible(false)
                .build();

        List<MarketPlaceVenueConfig> configs = Arrays.asList(config1, config2);

        MarketPlaceVenueConfigDTO configDTO1 = new MarketPlaceVenueConfigDTO(1, "venue-1", true,"US");
        MarketPlaceVenueConfigDTO configDTO2 = new MarketPlaceVenueConfigDTO(2, "venue-2", false,"US");

        when(repository.findAll()).thenReturn(configs);
        when(mapper.toMarketPlaceVenueConfigDTO(config1)).thenReturn(configDTO1);
        when(mapper.toMarketPlaceVenueConfigDTO(config2)).thenReturn(configDTO2);

        // Act
        List<MarketPlaceVenueConfigDTO> result = service.getAllMarketPlaceVenueConfigs();

        // Assert
        assertThat(result).isNotNull()
                .hasSize(2)
                .containsExactly(configDTO1, configDTO2);
        verify(repository).findAll();
        verify(mapper, times(2)).toMarketPlaceVenueConfigDTO(any(MarketPlaceVenueConfig.class));
    }

    @Test
    @DisplayName("getAllMarketPlaceVenueConfigs should return empty list when no data exists")
    void getAllMarketPlaceVenueConfigs_WhenNoDataExists_ShouldReturnEmptyList() {
        // Arrange
        when(repository.findAll()).thenReturn(List.of());

        // Act
        List<MarketPlaceVenueConfigDTO> result = service.getAllMarketPlaceVenueConfigs();

        // Assert
        assertThat(result).isNotNull().isEmpty();
        verify(repository).findAll();
        verifyNoInteractions(mapper);
    }

    @Test
    @DisplayName("deleteMarketPlaceVenueConfigById should delete venue config by venue ID")
    void deleteMarketPlaceVenueConfigById_ShouldDeleteConfig() {
        // Arrange
        doNothing().when(repository).deleteByVenueId(anyString());

        // Act
        service.deleteMarketPlaceVenueConfigById(VALID_VENUE_ID);

        // Assert
        verify(repository).deleteByVenueId(VALID_VENUE_ID);
    }
}
package com.xo.marketplace.service;

import com.xo.backend.client.venues.VenueInfoService;
import com.xo.backend.database.entity.fees.FeeConfigEntity;
import com.xo.backend.database.repository.fees.FeeConfigRepository;
import com.xo.backend.error.exceptions.MarketPlaceVenueConfigNotFoundException;
import com.xo.backend.model.dto.go.Address;
import com.xo.backend.model.dto.go.Country;
import com.xo.backend.model.dto.go.VenueOverviewDTO;
import com.xo.marketplace.database.MarketPlaceVenueConfig;
import com.xo.marketplace.dto.MarketPlaceVenueConfigDTO;
import com.xo.marketplace.dto.VenueFeeConfigDTO;
import com.xo.marketplace.mapper.MarketPlaceVenueConfigMapper;
import com.xo.marketplace.mapper.VenueFeeConfigMapper;
import com.xo.marketplace.repository.MarketPlaceVenueConfigRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MarketPlaceVenueConfigServiceTest {

    @Mock
    private MarketPlaceVenueConfigRepository repository;

    @Mock
    private MarketPlaceVenueConfigMapper mapper;

    @Mock
    private VenueInfoService venueInfoService;

    @Mock
    private FeeConfigRepository feeConfigRepository;

    @Mock
    private VenueFeeConfigMapper venueFeeConfigMapper;

    @InjectMocks
    private MarketPlaceVenueConfigService service;

    private static final String VALID_VENUE_ID = "venue-123";
    private static final String NONEXISTENT_VENUE_ID = "nonexistent-venue";
    private static final Integer VALID_CONFIG_ID = 1;

    private MarketPlaceVenueConfig venueConfig;
    private MarketPlaceVenueConfigDTO venueConfigDTO;
    private FeeConfigEntity feeConfigEntity;
    private VenueFeeConfigDTO venueFeeConfigDTO;

    @BeforeEach
    void setUp() {
        venueConfig = MarketPlaceVenueConfig.builder()
                .id(VALID_CONFIG_ID)
                .venueId(VALID_VENUE_ID)
                .visible(true)
                .countryIso("US")
                .build();

        feeConfigEntity = FeeConfigEntity.builder()
                .id(1)
                .venueId(VALID_VENUE_ID)
                .appFeePerc(new BigDecimal("0.05"))
                .appFeeFixedPerTransaction(new BigDecimal("2.00"))
                .appFeeFixedPerEntryPass(new BigDecimal("1.00"))
                .appFeeMin(new BigDecimal("1.00"))
                .appFeeMax(new BigDecimal("50.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.00"))
                .pspFeePerc(new BigDecimal("0.029"))
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(true)
                .build();

        venueFeeConfigDTO = VenueFeeConfigDTO.builder()
                .venueId(VALID_VENUE_ID)
                .appFeePerc(new BigDecimal("0.05"))
                .appFeeFixedPerTransaction(new BigDecimal("2.00"))
                .appFeeFixedPerEntryPass(new BigDecimal("1.00"))
                .appFeeMin(new BigDecimal("1.00"))
                .appFeeMax(new BigDecimal("50.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.00"))
                .pspFeePerc(new BigDecimal("0.029"))
                .pspFeeFixed(new BigDecimal("0.30"))
                .passPspFees(true)
                .build();

        venueConfigDTO = MarketPlaceVenueConfigDTO.builder()
                .id(VALID_CONFIG_ID)
                .venueId(VALID_VENUE_ID)
                .visible(true)
                .countryIso("US")
                .feeConfig(null)
                .build();
    }

    @Test
    @DisplayName("getMarketPlaceVenueConfigByVenueId should return venue config when venue ID exists")
    void getMarketPlaceVenueConfigByVenueId_WhenVenueExists_ShouldReturnConfig() {
        // Arrange
        when(repository.findByVenueId(VALID_VENUE_ID)).thenReturn(Optional.of(venueConfig));
        when(mapper.toMarketPlaceVenueConfigDTO(venueConfig)).thenReturn(venueConfigDTO);
        when(feeConfigRepository.findByVenueId(VALID_VENUE_ID)).thenReturn(Optional.empty());

        // Act
        MarketPlaceVenueConfigDTO result = service.getMarketPlaceVenueConfigByVenueId(VALID_VENUE_ID);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.venueId()).isEqualTo(VALID_VENUE_ID);
        assertThat(result.visible()).isTrue();
        assertThat(result.feeConfig()).isNull();
        verify(repository).findByVenueId(VALID_VENUE_ID);
        verify(mapper).toMarketPlaceVenueConfigDTO(venueConfig);
        verify(feeConfigRepository).findByVenueId(VALID_VENUE_ID);
    }

    @Test
    @DisplayName("getMarketPlaceVenueConfigByVenueId should throw exception when venue ID does not exist")
    void getMarketPlaceVenueConfigByVenueId_WhenVenueDoesNotExist_ShouldThrowException() {
        // Arrange
        when(repository.findByVenueId(NONEXISTENT_VENUE_ID)).thenReturn(Optional.empty());

        // Act & Assert
        assertThatThrownBy(() -> service.getMarketPlaceVenueConfigByVenueId(NONEXISTENT_VENUE_ID))
                .isInstanceOf(MarketPlaceVenueConfigNotFoundException.class);
        verify(repository).findByVenueId(NONEXISTENT_VENUE_ID);
        verifyNoInteractions(mapper);
    }

    @Test
    @DisplayName("getAllMarketPlaceVenueConfigs should return all venue configs when data exists")
    void getAllMarketPlaceVenueConfigs_WhenDataExists_ShouldReturnAllConfigs() {
        // Arrange
        MarketPlaceVenueConfig config1 = MarketPlaceVenueConfig.builder()
                .id(1)
                .venueId("venue-1")
                .visible(true)
                .countryIso("US")
                .build();

        MarketPlaceVenueConfig config2 = MarketPlaceVenueConfig.builder()
                .id(2)
                .venueId("venue-2")
                .visible(false)
                .countryIso("CA")
                .build();

        List<MarketPlaceVenueConfig> configs = Arrays.asList(config1, config2);

        MarketPlaceVenueConfigDTO configDTO1 = MarketPlaceVenueConfigDTO.builder()
                .id(1).venueId("venue-1").visible(true).countryIso("US").feeConfig(null).build();
        MarketPlaceVenueConfigDTO configDTO2 = MarketPlaceVenueConfigDTO.builder()
                .id(2).venueId("venue-2").visible(false).countryIso("CA").feeConfig(null).build();

        when(repository.findAll()).thenReturn(configs);
        when(mapper.toMarketPlaceVenueConfigDTO(config1)).thenReturn(configDTO1);
        when(mapper.toMarketPlaceVenueConfigDTO(config2)).thenReturn(configDTO2);
        when(feeConfigRepository.findAll()).thenReturn(Collections.emptyList());

        // Act
        List<MarketPlaceVenueConfigDTO> result = service.getAllMarketPlaceVenueConfigs();

        // Assert
        assertThat(result).isNotNull().hasSize(2);
        assertThat(result.get(0).venueId()).isEqualTo("venue-1");
        assertThat(result.get(1).venueId()).isEqualTo("venue-2");
        verify(repository).findAll();
        verify(feeConfigRepository).findAll();
    }

    @Test
    @DisplayName("getAllMarketPlaceVenueConfigs should return empty list when no data exists")
    void getAllMarketPlaceVenueConfigs_WhenNoDataExists_ShouldReturnEmptyList() {
        // Arrange
        when(repository.findAll()).thenReturn(List.of());

        // Act
        List<MarketPlaceVenueConfigDTO> result = service.getAllMarketPlaceVenueConfigs();

        // Assert
        assertThat(result).isNotNull().isEmpty();
        verify(repository).findAll();
    }

    @Test
    @DisplayName("deleteMarketPlaceVenueConfigById should delete venue config and fee config by venue ID")
    void deleteMarketPlaceVenueConfigById_ShouldDeleteConfig() {
        // Arrange
        doNothing().when(repository).deleteByVenueId(anyString());
        when(feeConfigRepository.findByVenueId(VALID_VENUE_ID)).thenReturn(Optional.of(feeConfigEntity));
        doNothing().when(feeConfigRepository).delete(any(FeeConfigEntity.class));

        // Act
        service.deleteMarketPlaceVenueConfigById(VALID_VENUE_ID);

        // Assert
        verify(repository).deleteByVenueId(VALID_VENUE_ID);
        verify(feeConfigRepository).findByVenueId(VALID_VENUE_ID);
        verify(feeConfigRepository).delete(feeConfigEntity);
    }

    // Tests for fee configuration functionality

    @Test
    @DisplayName("getMarketPlaceVenueConfigByVenueId should return venue config with fee config when both exist")
    void getMarketPlaceVenueConfigByVenueId_WhenBothExist_ShouldReturnConfigWithFeeConfig() {
        // Arrange
        when(repository.findByVenueId(VALID_VENUE_ID)).thenReturn(Optional.of(venueConfig));
        when(mapper.toMarketPlaceVenueConfigDTO(venueConfig)).thenReturn(venueConfigDTO);
        when(feeConfigRepository.findByVenueId(VALID_VENUE_ID)).thenReturn(Optional.of(feeConfigEntity));
        when(venueFeeConfigMapper.toVenueFeeConfigDTO(feeConfigEntity)).thenReturn(venueFeeConfigDTO);

        // Act
        MarketPlaceVenueConfigDTO result = service.getMarketPlaceVenueConfigByVenueId(VALID_VENUE_ID);

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.venueId()).isEqualTo(VALID_VENUE_ID);
        assertThat(result.feeConfig()).isNotNull();
        assertThat(result.feeConfig().venueId()).isEqualTo(VALID_VENUE_ID);
        assertThat(result.feeConfig().appFeePerc()).isEqualTo(new BigDecimal("0.05"));
        verify(repository).findByVenueId(VALID_VENUE_ID);
        verify(feeConfigRepository).findByVenueId(VALID_VENUE_ID);
        verify(venueFeeConfigMapper).toVenueFeeConfigDTO(feeConfigEntity);
    }

    @Test
    @DisplayName("createOrUpdateBatchMarketPlaceVenueConfig should update existing venue config and existing fee config")
    void createOrUpdateBatchMarketPlaceVenueConfig_WhenUpdatingBothExisting_ShouldUpdateBoth() {
        // Arrange
        VenueFeeConfigDTO updatedFeeConfigDTO = VenueFeeConfigDTO.builder()
                .venueId(VALID_VENUE_ID)
                .appFeePerc(new BigDecimal("0.10"))
                .appFeeFixedPerTransaction(new BigDecimal("3.00"))
                .appFeeFixedPerEntryPass(new BigDecimal("1.50"))
                .appFeeMin(new BigDecimal("2.00"))
                .appFeeMax(new BigDecimal("100.00"))
                .appFeeToCustomerPerc(new BigDecimal("1.00"))
                .pspFeePerc(new BigDecimal("0.035"))
                .pspFeeFixed(new BigDecimal("0.50"))
                .passPspFees(false)
                .build();

        MarketPlaceVenueConfigDTO updateConfigDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId(VALID_VENUE_ID)
                .visible(false)
                .feeConfig(updatedFeeConfigDTO)
                .build();

        when(repository.findAllByVenueIdIn(Set.of(VALID_VENUE_ID))).thenReturn(List.of(venueConfig));
        when(feeConfigRepository.findAll()).thenReturn(List.of(feeConfigEntity));
        when(repository.saveAll(any())).thenReturn(List.of(venueConfig));
        when(feeConfigRepository.saveAll(any())).thenReturn(List.of(feeConfigEntity));
        when(mapper.toMarketPlaceVenueConfigDTO(venueConfig)).thenReturn(updateConfigDTO);
        when(venueFeeConfigMapper.toVenueFeeConfigDTO(feeConfigEntity)).thenReturn(updatedFeeConfigDTO);

        // Act
        List<MarketPlaceVenueConfigDTO> result = service.createOrUpdateBatchMarketPlaceVenueConfig(List.of(updateConfigDTO));

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0).venueId()).isEqualTo(VALID_VENUE_ID);
        assertThat(result.get(0).visible()).isFalse();
        assertThat(result.get(0).feeConfig()).isNotNull();
        assertThat(result.get(0).feeConfig().appFeePerc()).isEqualTo(new BigDecimal("0.10"));
        verify(repository).saveAll(any());
        verify(feeConfigRepository).saveAll(any());
    }

    @Test
    @DisplayName("createOrUpdateBatchMarketPlaceVenueConfig should handle venue config without fee config")
    void createOrUpdateBatchMarketPlaceVenueConfig_WhenNoFeeConfig_ShouldOnlyUpdateVenueConfig() {
        // Arrange
        MarketPlaceVenueConfigDTO updateConfigDTO = MarketPlaceVenueConfigDTO.builder()
                .venueId(VALID_VENUE_ID)
                .visible(false)
                .feeConfig(null)
                .build();

        when(repository.findAllByVenueIdIn(Set.of(VALID_VENUE_ID))).thenReturn(List.of(venueConfig));
        when(feeConfigRepository.findAll()).thenReturn(Collections.emptyList());
        when(repository.saveAll(any())).thenReturn(List.of(venueConfig));
        when(mapper.toMarketPlaceVenueConfigDTO(venueConfig)).thenReturn(updateConfigDTO);

        // Act
        List<MarketPlaceVenueConfigDTO> result = service.createOrUpdateBatchMarketPlaceVenueConfig(List.of(updateConfigDTO));

        // Assert
        assertThat(result).hasSize(1);
        assertThat(result.get(0).venueId()).isEqualTo(VALID_VENUE_ID);
        assertThat(result.get(0).visible()).isFalse();
        assertThat(result.get(0).feeConfig()).isNull();
        verify(repository).saveAll(any());
        verify(feeConfigRepository, never()).saveAll(any());
    }

    private VenueOverviewDTO createMockVenueOverviewDTO() {
        // Create a mock VenueOverviewDTO - simplified version for testing
        Country country = new Country("United States", "US", "+1", "America/New_York");
        Address address = Address.builder()
                .country(country)
                .city("New York")
                .addressLine1("123 Test St")
                .postCode("10001")
                .build();

        return VenueOverviewDTO.builder()
                .storeId(123L)
                .name("Test Venue")
                .companyAddress(address)
                .currency("USD")
                .currencyIsoCode("USD")
                .timeZone("America/New_York")
                .build();
    }
}
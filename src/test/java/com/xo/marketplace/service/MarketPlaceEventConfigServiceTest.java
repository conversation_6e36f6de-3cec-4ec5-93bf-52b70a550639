package com.xo.marketplace.service;

import com.xo.marketplace.database.MarketPlaceEventConfig;
import com.xo.marketplace.dto.MarketPlaceEventConfigDTO;
import com.xo.marketplace.mapper.MarketPlaceEventConfigMapper;
import com.xo.marketplace.repository.MarketPlaceEventConfigRepository;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class MarketPlaceEventConfigServiceTest {


    @Mock
    private MarketPlaceEventConfigRepository marketPlaceEventConfigRepository;

    @Mock
    private MarketPlaceEventConfigMapper marketPlaceEventConfigMapper;

    @InjectMocks
    private MarketPlaceEventConfigService marketPlaceEventConfigService;

    private static MarketPlaceEventConfig marketPlaceEventConfig;

    private static final Integer EVENT_ID = 1;

    @BeforeAll
    static void setUp() {
        marketPlaceEventConfig = MarketPlaceEventConfig.builder().id(1).eventId(EVENT_ID).visible(true).createdBy(1).updatedBy(1).build();
    }

    @Test
    void testGetMarketPlaceEventConfigByEventId() {
        MarketPlaceEventConfigDTO marketPlaceConfigDTO=MarketPlaceEventConfigDTO.builder().visible(true).build();
        when(marketPlaceEventConfigRepository.findByEventId(any())).thenReturn(Optional.of(marketPlaceEventConfig));
        when(marketPlaceEventConfigMapper.toMarketPlaceEventConfigDTO(any())).thenReturn(marketPlaceConfigDTO);
        assertEquals(marketPlaceConfigDTO,marketPlaceEventConfigService.getMarketPlaceEventConfigByEventId(EVENT_ID));
    }

    @Test
    void testGetAllMarketPlaceEventConfig() {
        MarketPlaceEventConfigDTO marketPlaceConfigDTO=MarketPlaceEventConfigDTO.builder().visible(true).build();
        when(marketPlaceEventConfigRepository.findAll()).thenReturn(List.of(marketPlaceEventConfig));
        when(marketPlaceEventConfigMapper.toMarketPlaceEventConfigDTO(any())).thenReturn(marketPlaceConfigDTO);
        List<MarketPlaceEventConfigDTO> marketPlaceConfigDTOS=marketPlaceEventConfigService.getAllMarketPlaceEventConfigs();
        assertEquals(1, marketPlaceConfigDTOS.size());
        assertEquals(marketPlaceConfigDTO,marketPlaceConfigDTOS.getFirst());
    }

    @Test
    void testDeleteMarketPlaceEventConfigByEventId() {
       marketPlaceEventConfigService.deleteMarketPlaceEventConfigById(EVENT_ID);
       assertEquals(0, marketPlaceEventConfigRepository.findAll().size());
    }


}
